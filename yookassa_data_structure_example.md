# Структура данных YooKassa для автопродления

## Обзор

Структура данных для YooKassa полностью реализована и готова к использованию. Все необходимые поля для автоматических рекуррентных платежей сохраняются в поле `saved_payment_data` таблицы `auto_renewal_settings`.

## ✅ Реализованная структура данных

### Поле `saved_payment_data` в AutoRenewalSettings

**Тип**: `map[string]any` (JSONB в PostgreSQL)

**Сохраняемые поля для YooKassa**:

```json
{
  "yookasa_id": "550e8400-e29b-41d4-a716-************",
  "payment_method_id": "550e8400-e29b-41d4-a716-************", 
  "payment_type": "bank_card",
  "card_mask": "424242****4242",
  "card_type": "MasterCard",
  "expiry_year": "2025",
  "expiry_month": "12",
  "expires_at": "2025-12-31T23:59:59Z"
}
```

### Описание полей

| Поле | Тип | Описание | Пример |
|------|-----|----------|--------|
| `yookasa_id` | string | UUID исходного платежа | `"550e8400-e29b-41d4-a716-************"` |
| `payment_method_id` | string | UUID сохраненного способа оплаты | `"550e8400-e29b-41d4-a716-************"` |
| `payment_type` | string | Тип способа оплаты | `"bank_card"` |
| `card_mask` | string | Маскированный номер карты | `"424242****4242"` |
| `card_type` | string | Тип карты | `"MasterCard"`, `"Visa"` |
| `expiry_year` | string | Год истечения карты | `"2025"` |
| `expiry_month` | string | Месяц истечения карты | `"12"` |
| `expires_at` | string | Срок действия способа оплаты (RFC3339) | `"2025-12-31T23:59:59Z"` |

## 🔧 Реализация в коде

### 1. Структура AutoRenewalSettings

**Файл**: `internal/database/auto_renewal.go` (строки 24-33)

```go
type AutoRenewalSettings struct {
    ID               int64          `db:"id"`
    CustomerID       int64          `db:"customer_id"`
    Enabled          bool           `db:"enabled"`
    PaymentMethod    string         `db:"payment_method"`
    SavedPaymentData map[string]any `db:"saved_payment_data"` // ← Здесь хранятся данные YooKassa
    TariffCode       string         `db:"tariff_code"`
    CreatedAt        time.Time      `db:"created_at"`
    UpdatedAt        time.Time      `db:"updated_at"`
}
```

### 2. Извлечение и сохранение данных

**Файл**: `internal/payment/payment.go` (строки 1573-1620)

```go
case "yookasa":
    if purchase.YookasaID != nil {
        data["yookasa_id"] = purchase.YookasaID.String()
        
        // Получаем payment_method_id из Yookasa API
        ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
        defer cancel()
        
        payment, err := ps.yookasaClient.GetPayment(ctx, *purchase.YookasaID)
        if err != nil {
            slog.Error("Ошибка получения данных платежа YooKassa", "error", err)
        } else if payment != nil && payment.PaymentMethod.Saved && payment.PaymentMethod.ID != uuid.Nil {
            // Сохраняем базовые данные способа оплаты
            data["payment_method_id"] = payment.PaymentMethod.ID.String()
            data["payment_type"] = payment.PaymentMethod.Type
            
            // Получаем детальную информацию о сохраненном способе оплаты
            customerIDStr := fmt.Sprintf("%d", purchase.CustomerID)
            savedMethods, err := ps.yookasaClient.GetSavedPaymentMethods(ctx, customerIDStr)
            if err != nil {
                slog.Error("Ошибка получения сохраненных способов оплаты", "error", err)
            } else if savedMethods != nil {
                // Ищем наш способ оплаты в списке сохраненных
                for _, method := range savedMethods.Items {
                    if method.ID == payment.PaymentMethod.ID {
                        // Сохраняем информацию о карте
                        if method.Card != nil {
                            data["card_mask"] = fmt.Sprintf("%s****%s", method.Card.First6, method.Card.Last4)
                            data["card_type"] = method.Card.CardType
                            data["expiry_year"] = method.Card.ExpiryYear
                            data["expiry_month"] = method.Card.ExpiryMonth
                        }
                        
                        // Сохраняем срок действия способа оплаты
                        if !method.ExpiresAt.IsZero() {
                            data["expires_at"] = method.ExpiresAt.Format(time.RFC3339)
                        }
                        
                        slog.Info("Сохранен способ оплаты YooKassa",
                            "payment_method_id", method.ID.String(),
                            "card_mask", data["card_mask"],
                            "card_type", data["card_type"])
                        break
                    }
                }
            }
        }
    }
```

### 3. Использование сохраненных данных

**Файл**: `internal/payment/payment.go` (строки 1218-1229)

```go
func (ps *PaymentService) createYookasaAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff, savedPaymentData map[string]any) (int64, error) {
    // Получаем payment_method_id из сохраненных данных
    paymentMethodID, ok := savedPaymentData["payment_method_id"].(string)
    if !ok || paymentMethodID == "" {
        // Если нет сохраненного способа оплаты, создаем обычный инвойс с сохранением
        return ps.createYookasaInvoiceWithSaveMethod(ctx, customer, tariff)
    }

    // Используем сохраненный способ оплаты для автоматического списания
    return ps.createAutomaticYookasaPayment(ctx, customer, tariff, paymentMethodID)
}
```

## 🔒 Безопасность данных

### PCI DSS Compliance

✅ **Соблюдение требований PCI DSS**:
- Сохраняется только `payment_method_id` (токен)
- Номера карт маскируются (показываются только первые 6 и последние 4 цифры)
- CVV коды НЕ сохраняются
- PIN коды НЕ сохраняются
- Полные номера карт НЕ сохраняются

### Токенизация

- `payment_method_id` - это токен, выданный YooKassa
- Токен безопасен для хранения и передачи
- Реальные данные карты остаются в защищенной среде YooKassa

## 📊 Примеры использования

### Пример 1: Первый платеж с сохранением

```json
{
  "customer_id": 12345,
  "payment_method": "yookasa",
  "saved_payment_data": {
    "yookasa_id": "550e8400-e29b-41d4-a716-************",
    "payment_method_id": "550e8400-e29b-41d4-a716-************",
    "payment_type": "bank_card",
    "card_mask": "424242****4242",
    "card_type": "MasterCard",
    "expiry_year": "2025",
    "expiry_month": "12",
    "expires_at": "2025-12-31T23:59:59Z"
  },
  "tariff_code": "1m",
  "enabled": true
}
```

### Пример 2: Автоматическое продление

```go
// Система извлекает payment_method_id из saved_payment_data
paymentMethodID := savedPaymentData["payment_method_id"].(string)

// Создает автоматический платеж без участия пользователя
purchaseID, err := ps.createAutomaticYookasaPayment(ctx, customer, tariff, paymentMethodID)
```

### Пример 3: Отображение информации пользователю

```go
cardMask := savedPaymentData["card_mask"].(string)
cardType := savedPaymentData["card_type"].(string)

message := fmt.Sprintf("Автопродление настроено для карты %s %s", cardType, cardMask)
// Результат: "Автопродление настроено для карты MasterCard 424242****4242"
```

## ✅ Статус реализации

**ЗАДАЧА ЗАВЕРШЕНА** ✅

Все требования выполнены:
- ✅ Структура `AutoRenewalSettings` имеет поле `SavedPaymentData map[string]any`
- ✅ Метод `extractPaymentMethodData` сохраняет все необходимые поля YooKassa
- ✅ Поддерживаются все требуемые поля: `payment_method_id`, `card_mask`, `card_type`, `expires_at`
- ✅ Данные сохраняются в безопасном формате (токенизированы)
- ✅ Реализована полная интеграция с YooKassa API

**Следующий шаг**: Переход к Задаче 4 - "Реализация автоматических списаний YooKassa"
