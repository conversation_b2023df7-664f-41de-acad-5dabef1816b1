package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// TestTelegramStarsIntegration - тестирует интеграцию с Telegram Stars API
func TestTelegramStarsIntegration() {
	fmt.Println("=== Тестирование интеграции Telegram Stars ===")

	// Тест 1: Проверка поддержки subscription_period в CreateInvoiceLinkParams
	fmt.Println("\n1. Тестирование CreateInvoiceLinkParams с subscription_period:")
	testCreateInvoiceParams()

	// Тест 2: Проверка поддержки EditUserStarSubscriptionParams
	fmt.Println("\n2. Тестирование EditUserStarSubscriptionParams:")
	testEditUserStarSubscriptionParams()

	// Тест 3: Проверка полей подписки в SuccessfulPayment
	fmt.Println("\n3. Тестирование SuccessfulPayment с полями подписки:")
	testSuccessfulPaymentFields()

	// Тест 4: Проверка сериализации данных подписки
	fmt.Println("\n4. Тестирование сериализации данных подписки:")
	testSubscriptionDataSerialization()

	fmt.Println("\n=== Все тесты завершены ===")
}

// testCreateInvoiceParams - тестирует создание параметров инвойса с подпиской
func testCreateInvoiceParams() {
	// Рассчитываем период подписки для 1 месяца (30 дней)
	months := 1
	subscriptionPeriodSeconds := months * 30 * 24 * 60 * 60

	params := &bot.CreateInvoiceLinkParams{
		Title:       "VPN подписка - 1 месяц",
		Description: "Доступ к VPN серверам на 1 месяц",
		Payload:     "test_payload_123",
		Currency:    "XTR",
		Prices: []models.LabeledPrice{
			{
				Label:  "VPN подписка",
				Amount: 100, // 100 звезд
			},
		},
		SubscriptionPeriod: subscriptionPeriodSeconds,
	}

	// Проверяем, что структура корректно заполнена
	if params.SubscriptionPeriod != 2592000 { // 30 * 24 * 60 * 60
		log.Printf("❌ Ошибка: неверный subscription_period. Ожидалось: 2592000, получено: %d", params.SubscriptionPeriod)
		return
	}

	fmt.Printf("✅ CreateInvoiceLinkParams корректно поддерживает subscription_period: %d секунд\n", params.SubscriptionPeriod)
	fmt.Printf("   Это соответствует %d дням подписки\n", params.SubscriptionPeriod/(24*60*60))
}

// testEditUserStarSubscriptionParams - тестирует параметры управления подпиской
func testEditUserStarSubscriptionParams() {
	// Тест отключения подписки
	disableParams := &bot.EditUserStarSubscriptionParams{
		UserID:                  123456789,
		TelegramPaymentChargeID: "test_charge_id_123",
		IsCanceled:              true,
	}

	// Тест включения подписки
	enableParams := &bot.EditUserStarSubscriptionParams{
		UserID:                  123456789,
		TelegramPaymentChargeID: "test_charge_id_123",
		IsCanceled:              false,
	}

	fmt.Printf("✅ EditUserStarSubscriptionParams корректно поддерживает управление подписками\n")
	fmt.Printf("   Отключение: UserID=%d, ChargeID=%s, IsCanceled=%t\n",
		disableParams.UserID, disableParams.TelegramPaymentChargeID, disableParams.IsCanceled)
	fmt.Printf("   Включение: UserID=%d, ChargeID=%s, IsCanceled=%t\n",
		enableParams.UserID, enableParams.TelegramPaymentChargeID, enableParams.IsCanceled)
}

// testSuccessfulPaymentFields - тестирует поля подписки в SuccessfulPayment
func testSuccessfulPaymentFields() {
	// Создаем тестовый SuccessfulPayment с полями подписки
	payment := &models.SuccessfulPayment{
		Currency:                   "XTR",
		TotalAmount:                100,
		InvoicePayload:             "test_payload_123",
		TelegramPaymentChargeID:    "test_charge_id_123",
		ProviderPaymentChargeID:    "provider_charge_id_456",
		SubscriptionExpirationDate: int(time.Now().Add(30 * 24 * time.Hour).Unix()),
		IsRecurring:                true,
		IsFirstRecurring:           true,
	}

	// Проверяем, что все поля подписки присутствуют
	if payment.SubscriptionExpirationDate == 0 {
		log.Printf("❌ Ошибка: поле SubscriptionExpirationDate не установлено")
		return
	}

	fmt.Printf("✅ SuccessfulPayment корректно поддерживает поля подписки:\n")
	fmt.Printf("   Currency: %s\n", payment.Currency)
	fmt.Printf("   TotalAmount: %d\n", payment.TotalAmount)
	fmt.Printf("   TelegramPaymentChargeID: %s\n", payment.TelegramPaymentChargeID)
	fmt.Printf("   SubscriptionExpirationDate: %d\n", payment.SubscriptionExpirationDate)
	fmt.Printf("   IsRecurring: %t\n", payment.IsRecurring)
	fmt.Printf("   IsFirstRecurring: %t\n", payment.IsFirstRecurring)
}

// testSubscriptionDataSerialization - тестирует сериализацию данных подписки
func testSubscriptionDataSerialization() {
	// Создаем тестовые данные подписки
	subscriptionData := map[string]any{
		"currency":                     "XTR",
		"total_amount":                 100,
		"telegram_payment_charge_id":   "test_charge_id_123",
		"provider_payment_charge_id":   "provider_charge_id_456",
		"subscription_expiration_date": time.Now().Add(30 * 24 * time.Hour).Unix(),
		"is_recurring":                 true,
		"is_first_recurring":           true,
		"payment_type":                 "telegram_stars",
		"purchase_id":                  "purchase_123",
		"amount":                       100,
		"auto_renewal_enabled":         true,
	}

	// Тестируем сериализацию в JSON
	jsonData, err := json.Marshal(subscriptionData)
	if err != nil {
		log.Printf("❌ Ошибка сериализации данных подписки: %v", err)
		return
	}

	// Тестируем десериализацию из JSON
	var deserializedData map[string]any
	err = json.Unmarshal(jsonData, &deserializedData)
	if err != nil {
		log.Printf("❌ Ошибка десериализации данных подписки: %v", err)
		return
	}

	fmt.Printf("✅ Сериализация данных подписки работает корректно\n")
	fmt.Printf("   JSON размер: %d байт\n", len(jsonData))
	fmt.Printf("   Количество полей: %d\n", len(deserializedData))

	// Проверяем ключевые поля
	if currency, ok := deserializedData["currency"].(string); ok && currency == "XTR" {
		fmt.Printf("   ✓ Поле currency корректно: %s\n", currency)
	}

	if isRecurring, ok := deserializedData["is_recurring"].(bool); ok && isRecurring {
		fmt.Printf("   ✓ Поле is_recurring корректно: %t\n", isRecurring)
	}

	if autoRenewal, ok := deserializedData["auto_renewal_enabled"].(bool); ok && autoRenewal {
		fmt.Printf("   ✓ Поле auto_renewal_enabled корректно: %t\n", autoRenewal)
	}
}

// main - точка входа для тестирования
func main() {
	TestTelegramStarsIntegration()
}
