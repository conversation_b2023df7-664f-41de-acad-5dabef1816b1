{"greeting": "👋🏻 <b>Welcome to Unveil VPN!</b>\n\n<b>Experience fast and secure internet with our premium VPN service.</b>\n\n<b>Available locations:</b>\n 🇩🇪 Germany\n 🇳🇱 Netherlands\n 🇫🇮 Finland\n 🇹🇷 Turkey\n\n<b>How to connect:</b>\n• Click the <b>\"Connect\"</b> button\n• Follow the short instructions\n\nYour privacy is our priority!", "buy_button": "💰 Buy", "connect_button": "🔌 Connect", "back_button": "🔙 Back", "pricing_info": "Russian bank cards and cryptocurrency are accepted for payment", "month_1": "1 month", "month_3": "3 months", "month_6": "6 months", "month_12": "12 months", "crypto_button": "₿ Cryptocurrency (%d₽)", "card_button": "💳 Bank card (%d₽)", "pay_button": "💸 Pay", "subscription_active": "Your subscription is valid until: %s", "subscription_link": "\n\nSubscription link: %s", "no_subscription": "You don't have an active subscription", "subscription_activated": "Your subscription has been activated!", "feedback_button": "⭐ Feedback", "server_status_button": "🟢 Server Status", "support_button": "🆘 Support", "channel_button": "📢 Channel", "tos_button": "Terms Of Service", "subscription_expiring": "⚠️ <b>Subscription Alert</b> ⚠️\n\nYour subscription expires on %s\nTo continue using the service, please renew your subscription", "renew_subscription_button": "🔄 Renew Subscription", "invoice_description": "Subscription", "invoice_label": "Subscription", "invoice_title": "Subscription", "trial_button": "🔥 Try for free", "trial_activated": "Trial period activated", "trial_text": "Your trial version is active", "activate_trial_button": "Activate trial version", "referral_button": "🤝 Referrals", "referral_text": "Invited: %d", "referral_bonus_granted": "You have received a referral bonus!", "stars_button": "⭐ Telegram Stars (%d⭐)", "share_referral_button": "Share!", "web_app_button_text": "Connect", "tribute_button": "Tribute", "tariff_not_found": "Tariff not found or unavailable. Please select another plan.", "promo_ask_question": "🎟️ <b>Use promo code?</b>\n\nDo you have a discount promo code?", "promo_yes_button": "✅ Yes", "promo_no_button": "❌ No", "promo_input_request": "🎟️ <b>Enter promo code</b>\n\nEnter your promo code to get a discount:", "promo_cancel_button": "❌ Cancel", "promo_continue_button": "➡️ Continue", "promo_try_again_button": "🔄 Try again", "promo_success_applied": "🎉 <b>Promo code applied!</b>\n\nYou got <b>%d%%</b> discount", "promo_discount_applied": "🎟️ Discount applied: <b>%d%%</b>", "payment_methods_title": "💳 <b>Choose payment method</b>\n\nPlan: <b>%s</b>", "promo_error_not_found": "❌ Promo code does not exist", "promo_error_expired": "❌ Promo code has expired", "promo_error_inactive": "❌ Promo code is inactive", "promo_error_limit_reached": "❌ Promo code usage limit reached", "promo_error_already_used": "❌ Promo code has already been used", "promo_error_user_type": "❌ Promo code is not available for your user type", "promo_error_tariff_type": "❌ Promo code is not applicable to the selected plan", "promo_error_general": "❌ Error applying promo code. Please try again.", "auto_renewal_24h_notification": "🔄 <b>Auto-renewal in 24 hours</b>\n\n📦 Plan: <b>%s</b>\n⏰ Expires: <b>%s</b>\n💰 Amount: <b>%d%s</b>\n💳 Payment method: <b>%s</b>\n\nYou can change settings or renew manually.", "auto_renewal_12h_notification": "⚠️ <b>Auto-renewal in 12 hours</b>\n\n📦 Plan: <b>%s</b>\n⏰ Expires: <b>%s</b>\n💰 Amount: <b>%d%s</b>\n💳 Payment method: <b>%s</b>\n\nLast chance to change settings.", "manual_renewal_button": "🛒 Renew manually", "change_payment_method_button": "💳 Change payment method", "disable_auto_renewal_button": "❌ Disable auto-renewal", "delete_payment_data_button": "🗑️ Delete payment data", "setup_auto_renewal_button": "⚙️ Setup auto-renewal", "back_to_subscription_button": "🔙 Back to subscription", "auto_renewal_setup_message": "⚙️ <b>Auto-renewal setup</b>\n\nAuto-renewal allows automatic subscription renewal without your participation.\n\nChoose an action:", "auto_renewal_enabled_message": "✅ <b>Auto-renewal enabled</b>", "auto_renewal_disabled": "Auto-renewal disabled", "auto_renewal_tariff_info": "📦 Plan: <b>%s</b>", "auto_renewal_price_info": "💰 Price: <b>%d%s</b>", "auto_renewal_payment_method_info": "💳 Payment method: <b>%s</b>", "auto_renewal_change_payment_message": "💳 <b>Change payment method</b>\n\nSelect a new payment method for auto-renewal:", "auto_renewal_disable_error": "❌ <b>Disable error</b>\n\nFailed to disable auto-renewal. Please try later.", "auto_renewal_disabled_success": "✅ <b>Auto-renewal disabled</b>\n\nAutomatic subscription renewal has been disabled.", "auto_renewal_delete_data_error": "❌ <b>Delete error</b>\n\nFailed to delete payment data. Please try later.", "auto_renewal_data_deleted_success": "✅ <b>Data deleted</b>\n\nAll saved payment data has been deleted.", "payment_method_card": "💳 Bank card", "payment_method_crypto": "₿ Cryptocurrency", "payment_method_stars": "⭐ Telegram Stars", "payment_method_tribute": "🏛️ Tribute", "auto_renewal_select_tariff_message": "📦 <b>Select plan for auto-renewal</b>\n\nChoose a plan that will be automatically renewed:", "auto_renewal_select_payment_message": "💳 <b>Select payment method</b>\n\nPlan: <b>%s</b>\n\nChoose a payment method for auto-renewal:", "auto_renewal_confirmation_message": "✅ <b>Confirm auto-renewal setup</b>\n\n📦 Plan: <b>%s</b>\n💰 Price: <b>%d%s</b>\n💳 Payment method: <b>%s</b>\n\nConfirm auto-renewal setup:", "auto_renewal_setup_error": "❌ <b>Setup error</b>\n\nFailed to setup auto-renewal. Please try later.", "auto_renewal_setup_success": "✅ <b>Auto-renewal configured!</b>\n\nAutomatic subscription renewal has been successfully enabled. You will receive notifications 24 and 12 hours before payment.", "confirm_button": "✅ Confirm", "auto_renewal_invoice_title": "🔄 Subscription Auto-renewal", "auto_renewal_invoice_description": "Automatic subscription renewal for %s plan", "cryptopay_auto_renewal_notification": "🔄 <b>Subscription Auto-renewal</b>\n\n💎 <b>CryptoPay - Fast and Secure!</b>\n\n📦 Plan: <b>%s</b>\n💰 Cost: <b>%d₽</b>\n⏰ Duration: <b>%d month(s)</b>\n💳 Payment method: <b>🪙 Cryptocurrency (USDT)</b>\n\n🚀 <b>CryptoPay Advantages:</b>\n• ⚡ Instant transfers\n• 🔒 High security\n• 💸 Low fees\n• 🌍 Available worldwide\n\n⚠️ <b>Important:</b> To renew your subscription, you need to pay the invoice within <b>24 hours</b>. If not paid, auto-renewal will be disabled.\n\n👇 Click the button below to pay:", "cryptopay_payment_button": "₿ Pay via CryptoPay", "cryptopay_cancel_auto_renewal_button": "❌ Cancel auto-renewal", "cryptopay_24h_notification": "🔔 <b>Auto-renewal Reminder</b>\n\n⏰ <b>24 hours left until subscription expires!</b>\n\n📦 Plan: <b>%s</b>\n💰 Amount: <b>%d₽</b>\n🪙 Payment method: <b>CryptoPay (USDT)</b>\n⏰ Expires: <b>%s</b>\n\n💡 <b>What will happen:</b>\n• In 24 hours subscription will expire\n• Invoice will be automatically created\n• You'll have 24 hours to pay\n• If not paid, auto-renewal will be disabled\n\n🎯 <b>We recommend:</b> Make sure you have enough USDT in your CryptoPay balance for automatic payment.", "cryptopay_12h_notification": "⚠️ <b><PERSON><PERSON> Reminder!</b>\n\n🚨 <b>12 hours left until subscription expires!</b>\n\n📦 Plan: <b>%s</b>\n💰 Amount: <b>%d₽</b>\n🪙 Payment method: <b>CryptoPay (USDT)</b>\n⏰ Expires: <b>%s</b>\n\n⏳ <b>Last chance:</b>\n• Check your USDT balance in CryptoPay\n• Prepare funds for payment\n• Invoice will be created in 12 hours\n\n💸 <b>Cost in USDT:</b> ~%d USDT (at current rate)\n\n🔄 If you don't want to renew - disable auto-renewal now!", "cryptopay_failed_notification": "❌ <b>CryptoPay Auto-renewal Error</b>\n\n🚫 <b>Failed to renew subscription automatically</b>\n\n📦 Plan: <b>%s</b>\n💰 Amount: <b>%d₽</b>\n🪙 Payment method: <b>CryptoPay</b>\n⏰ Expired: <b>%s</b>\n\n🔍 <b>Possible reasons:</b>\n• Insufficient balance\n• CryptoPay technical issues\n• Invoice expired\n• Network problems\n\n💡 <b>What to do:</b>\n• Check your USDT balance\n• Renew subscription manually\n• Set up auto-renewal again\n\n⚙️ Auto-renewal was automatically disabled to prevent repeated errors.", "payment_method_cryptopay": "CryptoPay", "usdt_approximate_amount": "~%d USDT", "cryptopay_quick_payment_button": "⚡ Quick CryptoPay Payment", "cryptopay_quick_payment_processing": "🔄 Creating CryptoPay invoice...", "cryptopay_quick_payment_error": "❌ Error creating CryptoPay invoice. Please try again later.", "cryptopay_3d_notification": "⏰ <b>Subscription Renewal Reminder</b>\n\n📦 Plan: <b>%s</b>\n💰 Price: <b>%d ₽</b>\n📅 Subscription expires: <b>%s</b>\n\n🔔 Your subscription expires in <b>3 days</b>. We recommend renewing early to avoid service interruptions.\n\n💡 <i>You can renew your subscription right now or set up automatic renewal.</i>", "cryptopay_1d_notification": "⚠️ <b><PERSON>rgent Renewal Reminder</b>\n\n📦 Plan: <b>%s</b>\n💰 Price: <b>%d ₽</b>\n📅 Subscription expires: <b>%s</b>\n\n🔥 Your subscription expires in <b>1 day</b>! Renew today to maintain access to the service.\n\n⚡ Use quick payment below or go to the purchase menu.", "cryptopay_6h_notification": "🚨 <b>Warning! Subscription expires in 6 hours</b>\n\n📦 Plan: <b>%s</b>\n💰 Price: <b>%d ₽</b>\n📅 Subscription expires: <b>%s</b>\n\n⏰ Less than <b>6 hours</b> left until expiration. Renew now to avoid service interruption.\n\n⚡ Use quick payment for instant renewal.", "cryptopay_1h_notification": "🔴 <b>CRITICAL! Subscription expires in 1 hour</b>\n\n📦 Plan: <b>%s</b>\n💰 Price: <b>%d ₽</b>\n📅 Subscription expires: <b>%s</b>\n\n⚡ Only <b>1 hour</b> left until expiration! Renew IMMEDIATELY to maintain access.\n\n🚀 Use quick payment for instant access restoration.", "cryptopay_expired_notification": "🚨 <b>Subscription Expired!</b>\n\n📦 Plan: <b>%s</b>\n💰 Renewal price: <b>%d ₽</b>\n📅 Expired on: <b>%s</b>\n\n❌ Your subscription has expired. Service access is suspended.\n\n🔄 Renew your subscription now to restore access to VPN servers.\n\n⚡ Use quick payment or go to the purchase menu.", "auto_renewal_reenable_message": "🔄 <b>Re-enable auto-renewal</b>\n\n💾 You have saved payment method data. Choose an action:", "saved_payment_method": "Saved payment method", "auto_renewal_use_saved_method": "💳 Use saved method", "auto_renewal_setup_new_method": "🆕 Set up new method", "auto_renewal_no_saved_method_error": "❌ Error: saved payment method data not found", "auto_renewal_enable_error": "❌ Error enabling auto-renewal. Please try again later", "auto_renewal_enabled_with_saved_method": "✅ <b>Auto-renewal enabled!</b>\n\n📦 Plan: <b>%s</b>\n💳 Payment method: %s\n\n🔄 Subscription will be automatically renewed when it expires"}