package main

import (
	"context"
	"crypto/tls"
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"remnawave-tg-shop-bot/internal/cache"
	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/cryptopay"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/handler"
	"remnawave-tg-shop-bot/internal/notification"
	"remnawave-tg-shop-bot/internal/payment"
	"remnawave-tg-shop-bot/internal/remnawave"
	"remnawave-tg-shop-bot/internal/service"
	"remnawave-tg-shop-bot/internal/sync"
	"remnawave-tg-shop-bot/internal/translation"
	"remnawave-tg-shop-bot/internal/tribute"
	"remnawave-tg-shop-bot/internal/yookasa"
	"strconv"
	"strings"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/robfig/cron/v3"
)

func main() {
	slog.SetDefault(slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{Level: slog.LevelDebug})))

	ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt)
	defer cancel()

	config.InitConfig()

	tm := translation.GetInstance()
	err := tm.InitTranslations("./translations")
	if err != nil {
		panic(err)
	}

	pool, err := initDatabase(ctx, config.DadaBaseUrl())
	if err != nil {
		panic(err)
	}

	err = database.RunMigrations(ctx, &database.MigrationConfig{Direction: "up", MigrationsPath: "./db/migrations", Steps: 0}, pool)
	if err != nil {
		panic(err)
	}
	cache := cache.NewCache(30 * time.Minute)
	customerRepository := database.NewCustomerRepository(pool)
	purchaseRepository := database.NewPurchaseRepository(pool)
	referralRepository := database.NewReferralRepository(pool)
	broadcastTaskRepository := database.NewBroadcastTaskRepository(pool)
	broadcastTaskService := service.NewBroadcastTaskService(broadcastTaskRepository)
	tariffRepository := database.NewTariffRepository(pool)
	promoCodeRepository := database.NewPromoCodeRepository(pool)
	promoCodeUsageRepository := database.NewPromoCodeUsageRepository(pool)
	autoRenewalRepository := database.NewAutoRenewalRepository(pool)

	// --- Автосоздание дефолтных тарифов, если их нет ---
	tariffs, err := tariffRepository.GetAll(ctx, false)
	if err != nil {
		panic(fmt.Sprintf("Ошибка получения тарифов: %v", err))
	}
	if len(tariffs) == 0 {
		defaultTariffs := []database.Tariff{
			{Code: "1m", Title: "1 месяц", PriceRUB: 199, PriceStars: 119, Active: true},
			{Code: "3m", Title: "3 месяца", PriceRUB: 499, PriceStars: 289, Active: true},
			{Code: "6m", Title: "6 месяцев", PriceRUB: 899, PriceStars: 509, Active: true},
			{Code: "12m", Title: "12 месяцев", PriceRUB: 1699, PriceStars: 959, Active: true},
		}
		for _, t := range defaultTariffs {
			_, err := tariffRepository.Create(ctx, &t)
			if err != nil {
				panic(fmt.Sprintf("Ошибка создания дефолтного тарифа %s: %v", t.Code, err))
			}
		}
		slog.Info("Дефолтные тарифы успешно созданы")
	}

	cryptoPayClient := cryptopay.NewCryptoPayClient(config.CryptoPayUrl(), config.CryptoPayToken())
	remnawaveClient := remnawave.NewClient(config.RemnawaveUrl(), config.RemnawaveToken(), config.RemnawaveMode())
	yookasaClient := yookasa.NewClient(config.YookasaUrl(), config.YookasaShopId(), config.YookasaSecretKey())

	// Валидация токена бота
	if err := validateTelegramToken(config.TelegramToken()); err != nil {
		slog.Error("Invalid Telegram bot token", "error", err)
		panic(fmt.Errorf("invalid Telegram bot token: %w", err))
	}

	// Создаем HTTP-клиент с увеличенными таймаутами и отключенным HTTP2 для стабильности
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			// Настройки таймаутов для избежания "http2: timeout awaiting response headers"
			TLSHandshakeTimeout:   10 * time.Second,
			ResponseHeaderTimeout: 20 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,

			// Настройки соединений
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,

			// Настройки для стабильности
			DisableKeepAlives:  false,
			DisableCompression: false,

			// Принудительно отключаем HTTP2 через TLSNextProto
			TLSNextProto: make(map[string]func(authority string, c *tls.Conn) http.RoundTripper),
		},
	}

	slog.Info("Creating Telegram bot with enhanced HTTP client settings")
	b, err := bot.New(config.TelegramToken(),
		bot.WithWorkers(3),
		bot.WithHTTPClient(30*time.Second, httpClient),
	)
	if err != nil {
		slog.Error("Failed to create Telegram bot", "error", err)
		panic(fmt.Errorf("failed to create Telegram bot: %w", err))
	}

	promoCodeService := service.NewPromoCodeService(promoCodeRepository, promoCodeUsageRepository)
	paymentService := payment.NewPaymentService(tm, purchaseRepository, remnawaveClient, customerRepository, b, cryptoPayClient, yookasaClient, referralRepository, cache, promoCodeService, tariffRepository)

	// --- Сервисы автопродления ---
	autoRenewalService := service.NewAutoRenewalService(autoRenewalRepository, customerRepository, tariffRepository, b, paymentService)
	autoRenewalNotificationService := service.NewAutoRenewalNotificationService(autoRenewalRepository, tariffRepository, b, tm)

	// Устанавливаем интерфейс автопродления в PaymentService для автоматического включения после оплаты
	paymentService.SetAutoRenewalInterface(autoRenewalService)

	// Настраиваем dependency injection для управления подписками Telegram Stars
	paymentService.SetAutoRenewalRepository(autoRenewalRepository)
	autoRenewalService.SetTelegramStarsSubscriptionManager(paymentService)

	cronScheduler := setupInvoiceChecker(purchaseRepository, cryptoPayClient, paymentService, yookasaClient)
	if cronScheduler != nil {
		cronScheduler.Start()
		defer cronScheduler.Stop()
	}

	subService := notification.NewSubscriptionService(customerRepository, b, tm)
	promoCodeNotificationService := notification.NewPromoCodeNotificationService(promoCodeService, customerRepository, promoCodeUsageRepository, tariffRepository, b)

	subscriptionNotificationCronScheduler := setupSubscriptionNotifier(subService)
	subscriptionNotificationCronScheduler.Start()
	defer subscriptionNotificationCronScheduler.Stop()

	promoCodeNotificationCronScheduler := setupPromoCodeNotifier(promoCodeNotificationService)
	promoCodeNotificationCronScheduler.Start()
	defer promoCodeNotificationCronScheduler.Stop()

	// --- Планировщики автопродления ---
	autoRenewalNotificationCronScheduler := setupAutoRenewalNotifier(autoRenewalNotificationService)
	autoRenewalNotificationCronScheduler.Start()
	defer autoRenewalNotificationCronScheduler.Stop()

	autoRenewalProcessorCronScheduler := setupAutoRenewalProcessor(autoRenewalService)
	autoRenewalProcessorCronScheduler.Start()
	defer autoRenewalProcessorCronScheduler.Stop()

	syncService := sync.NewSyncService(remnawaveClient, customerRepository)

	// --- Канал для сигнализации планировщика рассылок ---
	broadcastWakeup := make(chan struct{}, 1)

	h := handler.NewHandler(syncService, paymentService, tm, customerRepository, purchaseRepository, cryptoPayClient, yookasaClient, referralRepository, cache, broadcastTaskService, broadcastWakeup, tariffRepository, promoCodeService, autoRenewalService)

	// --- Обработчик автопродления ---
	autoRenewalHandler := handler.NewAutoRenewalHandler(autoRenewalService, customerRepository, tariffRepository, tm, paymentService)

	// Безопасная инициализация бота с retry логикой
	me, err := initializeBotSafely(b)
	if err != nil {
		slog.Error("Failed to initialize Telegram bot", "error", err)
		panic(fmt.Errorf("failed to initialize Telegram bot: %w", err))
	}

	slog.Info("Telegram bot initialized successfully", "bot_username", me.Username, "bot_id", me.ID)

	config.SetBotURL(fmt.Sprintf("https://t.me/%s", me.Username))

	// Запускаем мониторинг здоровья бота в отдельной горутине
	go monitorBotHealth(b)

	b.RegisterHandler(bot.HandlerTypeMessageText, "/start", bot.MatchTypePrefix, h.StartCommandHandler)
	b.RegisterHandler(bot.HandlerTypeMessageText, "/connect", bot.MatchTypeExact, h.ConnectCommandHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeMessageText, "/sync", bot.MatchTypeExact, h.SyncUsersCommandHandler, isAdminMiddleware)
	b.RegisterHandler(bot.HandlerTypeMessageText, "/admin", bot.MatchTypeExact, h.AdminHandler.AdminCommandHandler, isAdminMiddleware)

	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackReferral, bot.MatchTypeExact, h.ReferralCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackBuy, bot.MatchTypeExact, h.BuyCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackTrial, bot.MatchTypeExact, h.TrialCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackActivateTrial, bot.MatchTypeExact, h.ActivateTrialCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackStart, bot.MatchTypeExact, h.StartCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackSell, bot.MatchTypePrefix, h.SellCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackConnect, bot.MatchTypeExact, h.ConnectCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackPaymentSettings, bot.MatchTypeExact, h.PaymentSettingsCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackPayment, bot.MatchTypePrefix, h.PaymentCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "get_subscription_link", bot.MatchTypeExact, h.GetSubscriptionLinkHandler, h.CreateCustomerIfNotExistMiddleware)

	// Промокоды
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackPromoCodeYes, bot.MatchTypePrefix, h.PromoCodeHandler.PromoCodeYesHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackPromoCodeNo, bot.MatchTypePrefix, h.PromoCodeHandler.PromoCodeNoHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackPromoCodeCancel, bot.MatchTypeExact, h.PromoCodeHandler.PromoCodeCancelHandler, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, handler.CallbackPromoCodeApply, bot.MatchTypeExact, h.PromoCodeHandler.PromoCodeApplyHandler, h.CreateCustomerIfNotExistMiddleware)

	// Новый flow покупки с промокодами
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "buy_promo_yes", bot.MatchTypeExact, h.CallbackHandler.HandleBuyPromoYes, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "buy_promo_no", bot.MatchTypeExact, h.CallbackHandler.HandleBuyPromoNo, h.CreateCustomerIfNotExistMiddleware)

	// Автопродление
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "auto_renewal_toggle", bot.MatchTypeExact, autoRenewalHandler.HandleAutoRenewalToggle, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "auto_renewal_disable", bot.MatchTypeExact, autoRenewalHandler.HandleAutoRenewalDisable, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "auto_renewal_change_payment", bot.MatchTypeExact, autoRenewalHandler.HandleAutoRenewalChangePayment, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "auto_renewal_set_payment", bot.MatchTypePrefix, autoRenewalHandler.HandleAutoRenewalSetPaymentMethod, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "auto_renewal_delete_data", bot.MatchTypeExact, autoRenewalHandler.HandleAutoRenewalDeleteData, h.CreateCustomerIfNotExistMiddleware)

	// Flow настройки автопродления
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "auto_renewal_setup_start", bot.MatchTypeExact, autoRenewalHandler.HandleAutoRenewalSetupStart, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "auto_renewal_select_tariff", bot.MatchTypePrefix, autoRenewalHandler.HandleAutoRenewalSelectTariff, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "auto_renewal_setup_payment", bot.MatchTypePrefix, autoRenewalHandler.HandleAutoRenewalSelectPaymentMethod, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "auto_renewal_confirm", bot.MatchTypePrefix, autoRenewalHandler.HandleAutoRenewalConfirm, h.CreateCustomerIfNotExistMiddleware)

	// Повторное включение автопродления с сохраненными данными
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "auto_renewal_reenable_saved", bot.MatchTypePrefix, autoRenewalHandler.HandleAutoRenewalReenableWithSaved, h.CreateCustomerIfNotExistMiddleware)
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "auto_renewal_setup_new", bot.MatchTypeExact, autoRenewalHandler.HandleAutoRenewalSetupStart, h.CreateCustomerIfNotExistMiddleware)

	// Быстрая оплата CryptoPay
	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "cryptopay_quick_payment", bot.MatchTypePrefix, autoRenewalHandler.HandleCryptoPayQuickPayment, h.CreateCustomerIfNotExistMiddleware)

	b.RegisterHandlerMatchFunc(func(update *models.Update) bool {
		return update.PreCheckoutQuery != nil
	}, h.PreCheckoutCallbackHandler, h.CreateCustomerIfNotExistMiddleware)

	b.RegisterHandlerMatchFunc(func(update *models.Update) bool {
		return update.Message != nil && update.Message.SuccessfulPayment != nil
	}, h.SuccessPaymentHandler)

	b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "", bot.MatchTypeContains, h.CallbackHandler.AdminCallbackHandler, isAdminMiddleware)

	b.RegisterHandlerMatchFunc(func(update *models.Update) bool {
		return update.Message != nil && update.Message.From != nil && update.Message.From.ID == config.GetAdminTelegramId()
	}, h.AdminTextHandler)

	mux := http.NewServeMux()
	mux.Handle("/healthcheck", fullHealthHandler(pool, remnawaveClient))
	if config.GetTributeWebHookUrl() != "" {
		tributeHandler := tribute.NewClient(paymentService, customerRepository)
		mux.Handle(config.GetTributeWebHookUrl(), tributeHandler.WebHookHandler())
	}

	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", config.GetHealthCheckPort()),
		Handler: mux,
	}
	go func() {
		log.Printf("Server listening on %s", srv.Addr)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server error: %v", err)
		}
	}()

	// --- Планировщик отложенных рассылок ---
	// --- Event-driven планировщик отложенных рассылок ---
	go func() {
		for {
			tasks, err := broadcastTaskService.GetAll(ctx)
			if err != nil {
				time.Sleep(10 * time.Second)
				continue
			}
			var nextSendAt *time.Time
			pending := false
			now := time.Now().UTC()
			for _, task := range tasks {
				if task.Status == database.BroadcastTaskStatusPending {
					pending = true
					if nextSendAt == nil || task.SendAt.Before(*nextSendAt) {
						nextSendAt = &task.SendAt
					}
				}
				if task.Status == database.BroadcastTaskStatusPending && !task.SendAt.After(now) {
					slog.Info("Отправка рассылки", "task_id", task.ID, "реальное_время", now)
					var users []database.Customer
					var err error
					if task.TargetAudience == "all" {
						users, err = customerRepository.GetAll(ctx)
					} else {
						users, err = customerRepository.GetUsersWithoutActiveSubscription(ctx)
					}
					if err != nil {
						slog.Error("Ошибка выборки пользователей", "err", err)
						continue
					}
					var success, failed int
					for _, user := range users {
						if user.TelegramID == config.GetAdminTelegramId() {
							continue
						}
						_, err := b.SendMessage(ctx, &bot.SendMessageParams{
							ChatID: user.TelegramID,
							Text:   task.Message,
						})
						if err != nil {
							failed++
						} else {
							success++
						}
						time.Sleep(35 * time.Millisecond)
					}
					broadcastTaskService.UpdateStatus(ctx, task.ID, database.BroadcastTaskStatusSent)
					msg := fmt.Sprintf(`📢 <b>Отложенная рассылка завершена!</b>

ID: <b>%d</b>
Текст: %s
Аудитория: %s
Время (запланировано): %s
Время (отправлено): %s

✅ Успешно: <b>%d</b>
🚫 Ошибок: <b>%d</b>`,
						task.ID,
						task.Message,
						handler.TranslateAudience(task.TargetAudience),
						handler.FormatTimeWithTZ(task.SendAt),
						handler.FormatTimeWithTZ(now),
						success, failed)
					_, _ = b.SendMessage(ctx, &bot.SendMessageParams{
						ChatID:    config.GetAdminTelegramId(),
						Text:      msg,
						ParseMode: models.ParseModeHTML,
					})
					// После отправки рассылки сразу переходим к следующей итерации, чтобы не логировать лишний раз сон
					continue
				}
			}
			var wait <-chan time.Time
			var sleepMsg string
			if pending && nextSendAt != nil {
				delta := nextSendAt.Sub(time.Now().UTC())
				if delta < 0 {
					delta = 0
				}
				wait = time.After(delta)
				sleepMsg = fmt.Sprintf("Планировщик уснул до %s (ждет рассылку)", handler.FormatTimeWithTZ(*nextSendAt))
			} else {
				wait = nil
				sleepMsg = "Планировщик уснул (ждет сигнал)"
			}
			slog.Info(sleepMsg)
			select {
			case <-broadcastWakeup:
				slog.Info("Планировщик проснулся по сигналу")
				// Получен сигнал — сразу проверяем задачи
			case <-wait:
				// Наступило время ближайшей рассылки
			}
		}
	}()

	slog.Info("Bot is starting...")
	b.Start(ctx)

	log.Println("Shutting down health server…")
	shutdownCtx, shutCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer shutCancel()
	if err := srv.Shutdown(shutdownCtx); err != nil {
		log.Printf("Health server shutdown error: %v", err)
	}
}

func fullHealthHandler(pool *pgxpool.Pool, rw *remnawave.Client) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		status := map[string]string{
			"status": "ok",
			"db":     "ok",
			"rw":     "ok",
			"time":   time.Now().Format(time.RFC3339),
		}

		dbCtx, dbCancel := context.WithTimeout(r.Context(), 5*time.Second)
		defer dbCancel()
		if err := pool.Ping(dbCtx); err != nil {
			w.WriteHeader(http.StatusServiceUnavailable)
			status["status"] = "fail"
			status["db"] = "error: " + err.Error()
		}

		rwCtx, rwCancel := context.WithTimeout(r.Context(), 5*time.Second)
		defer rwCancel()
		if err := rw.Ping(rwCtx); err != nil {
			w.WriteHeader(http.StatusServiceUnavailable)
			status["status"] = "fail"
			status["rw"] = "error: " + err.Error()
		}

		if status["status"] == "ok" {
			w.WriteHeader(http.StatusOK)
		}

		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"status":"%s","db":"%s","remnawave":"%s","time":"%s"}`,
			status["status"], status["db"], status["rw"], status["time"])
	})
}

func isAdminMiddleware(next bot.HandlerFunc) bot.HandlerFunc {
	return func(ctx context.Context, b *bot.Bot, update *models.Update) {
		var userID int64
		if update.CallbackQuery != nil {
			userID = update.CallbackQuery.From.ID
		} else if update.Message != nil && update.Message.From != nil {
			userID = update.Message.From.ID
		}
		slog.Info("isAdminMiddleware", "user_id", userID, "admin_id", config.GetAdminTelegramId())
		if userID == config.GetAdminTelegramId() {
			next(ctx, b, update)
		}
	}
}

func setupSubscriptionNotifier(subService *notification.SubscriptionService) *cron.Cron {
	c := cron.New()

	_, err := c.AddFunc("0 16 * * *", func() {
		slog.Info("Running subscription notification check")

		err := subService.SendSubscriptionNotifications(context.Background())
		if err != nil {
			slog.Error("Error sending subscription notifications", "error", err)
		}
	})

	if err != nil {
		panic(err)
	}
	return c
}

func setupPromoCodeNotifier(promoCodeNotificationService *notification.PromoCodeNotificationService) *cron.Cron {
	c := cron.New()

	// Проверка истекающих промокодов каждый час
	_, err := c.AddFunc("0 * * * *", func() {
		slog.Info("Running promo code expiry notification check")

		err := promoCodeNotificationService.SendExpiringPromoCodeNotifications(context.Background())
		if err != nil {
			slog.Error("Error sending promo code expiry notifications", "error", err)
		}
	})

	if err != nil {
		panic(err)
	}

	// Ежедневный отчет по промокодам в 9:00
	_, err = c.AddFunc("0 9 * * *", func() {
		slog.Info("Running daily promo code report")

		err := promoCodeNotificationService.SendDailyPromoCodeReport(context.Background())
		if err != nil {
			slog.Error("Error sending daily promo code report", "error", err)
		}
	})

	if err != nil {
		panic(err)
	}

	return c
}

func setupAutoRenewalNotifier(autoRenewalNotificationService *service.AutoRenewalNotificationService) *cron.Cron {
	c := cron.New()

	// Оптимизированная система уведомлений об автопродлении (каждые 2 часа)
	// Максимум 5 уведомлений на пользователя: 3d, 1d, 6h, 1h, expired
	_, err := c.AddFunc("0 */2 * * *", func() {
		slog.Info("Running optimized auto renewal notification check")

		err := autoRenewalNotificationService.SendAllAutoRenewalNotifications(context.Background())
		if err != nil {
			slog.Error("Error sending auto renewal notifications", "error", err)
		}
	})

	if err != nil {
		panic(err)
	}

	return c
}

func setupAutoRenewalProcessor(autoRenewalService *service.AutoRenewalService) *cron.Cron {
	c := cron.New()

	// Обработка автопродлений каждые 30 минут
	_, err := c.AddFunc("*/30 * * * *", func() {
		slog.Info("Running auto renewal processing")

		err := autoRenewalService.ProcessAutoRenewals(context.Background())
		if err != nil {
			slog.Error("Error processing auto renewals", "error", err)
		}
	})

	if err != nil {
		panic(err)
	}

	return c
}

func initDatabase(ctx context.Context, connString string) (*pgxpool.Pool, error) {
	config, err := pgxpool.ParseConfig(connString)
	if err != nil {
		return nil, err
	}

	config.MaxConns = 20
	config.MinConns = 5

	return pgxpool.ConnectConfig(ctx, config)
}

func setupInvoiceChecker(
	purchaseRepository *database.PurchaseRepository,
	cryptoPayClient *cryptopay.Client,
	paymentService *payment.PaymentService,
	yookasaClient *yookasa.Client) *cron.Cron {
	if !config.IsYookasaEnabled() && !config.IsCryptoPayEnabled() {
		return nil
	}
	c := cron.New(cron.WithSeconds())

	if config.IsCryptoPayEnabled() {
		_, err := c.AddFunc("*/5 * * * * *", func() {
			ctx := context.Background()
			checkCryptoPayInvoice(ctx, purchaseRepository, cryptoPayClient, paymentService)
		})

		if err != nil {
			panic(err)
		}
	}

	if config.IsYookasaEnabled() {
		_, err := c.AddFunc("*/5 * * * * *", func() {
			ctx := context.Background()
			checkYookasaInvoice(ctx, purchaseRepository, yookasaClient, paymentService)
		})

		if err != nil {
			panic(err)
		}
	}

	return c
}

func checkYookasaInvoice(
	ctx context.Context,
	purchaseRepository *database.PurchaseRepository,
	yookasaClient *yookasa.Client,
	paymentService *payment.PaymentService,
) {
	pendingPurchases, err := purchaseRepository.FindByInvoiceTypeAndStatus(
		ctx,
		database.InvoiceTypeYookasa,
		database.PurchaseStatusPending,
	)
	if err != nil {
		log.Printf("Error finding pending purchases: %v", err)
		return
	}
	if len(*pendingPurchases) == 0 {
		return
	}

	for _, purchase := range *pendingPurchases {

		invoice, err := yookasaClient.GetPayment(ctx, *purchase.YookasaID)

		if err != nil {
			slog.Error("Error getting invoice", "invoiceId", purchase.YookasaID, err)
			continue
		}

		if invoice.IsCancelled() {
			err := paymentService.CancelPayment(purchase.ID)
			if err != nil {
				slog.Error("Error canceling invoice", "invoiceId", invoice.ID, "purchaseId", purchase.ID, err)
			}
			continue
		}

		if !invoice.Paid {
			continue
		}

		purchaseId, err := strconv.Atoi(invoice.Metadata["purchaseId"])
		if err != nil {
			slog.Error("Error parsing purchaseId", "invoiceId", invoice.ID, err)
		}
		ctxWithValue := context.WithValue(ctx, "username", invoice.Metadata["username"])
		err = paymentService.ProcessPurchaseById(ctxWithValue, int64(purchaseId))
		if err != nil {
			slog.Error("Error processing invoice", "invoiceId", invoice.ID, "purchaseId", purchaseId, err)
		} else {
			slog.Info("Invoice processed", "invoiceId", invoice.ID, "purchaseId", purchaseId)
		}

	}
}

func checkCryptoPayInvoice(
	ctx context.Context,
	purchaseRepository *database.PurchaseRepository,
	cryptoPayClient *cryptopay.Client,
	paymentService *payment.PaymentService,
) {
	pendingPurchases, err := purchaseRepository.FindByInvoiceTypeAndStatus(
		ctx,
		database.InvoiceTypeCrypto,
		database.PurchaseStatusPending,
	)
	if err != nil {
		log.Printf("Error finding pending purchases: %v", err)
		return
	}
	if len(*pendingPurchases) == 0 {
		return
	}

	var invoiceIDs []string

	for _, purchase := range *pendingPurchases {
		if purchase.CryptoInvoiceID != nil {
			invoiceIDs = append(invoiceIDs, fmt.Sprintf("%d", *purchase.CryptoInvoiceID))
		}
	}

	if len(invoiceIDs) == 0 {
		return
	}

	stringInvoiceIDs := strings.Join(invoiceIDs, ",")
	invoices, err := cryptoPayClient.GetInvoices("", "", "", stringInvoiceIDs, 0, 0)
	if err != nil {
		log.Printf("Error getting invoices: %v", err)
		return
	}

	for _, invoice := range *invoices {
		if invoice.InvoiceID != nil && invoice.IsPaid() {
			payload := strings.Split(invoice.Payload, "&")
			purchaseID, err := strconv.Atoi(strings.Split(payload[0], "=")[1])
			username := strings.Split(payload[1], "=")[1]
			ctxWithUsername := context.WithValue(ctx, "username", username)
			err = paymentService.ProcessPurchaseById(ctxWithUsername, int64(purchaseID))
			if err != nil {
				slog.Error("Error processing invoice", "invoiceId", invoice.InvoiceID, err)
			} else {
				slog.Info("Invoice processed", "invoiceId", invoice.InvoiceID, "purchaseId", purchaseID)
			}

		}
	}
}

// initializeBotSafely - безопасная инициализация бота с retry логикой и увеличенными таймаутами
func initializeBotSafely(b *bot.Bot) (*models.User, error) {
	const maxRetries = 5
	const baseDelay = 2 * time.Second

	for attempt := 1; attempt <= maxRetries; attempt++ {
		// Создаем контекст с таймаутом для каждой попытки
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)

		slog.Info("Attempting to initialize Telegram bot",
			"attempt", attempt,
			"max_retries", maxRetries,
			"timeout", "30s")

		// Попытка получить информацию о боте
		me, err := b.GetMe(ctx)
		if err == nil {
			// Успешно получили информацию о боте, теперь настраиваем меню
			_, menuErr := b.SetChatMenuButton(ctx, &bot.SetChatMenuButtonParams{
				MenuButton: &models.MenuButtonCommands{
					Type: models.MenuButtonTypeCommands,
				},
			})

			if menuErr != nil {
				slog.Warn("Failed to set chat menu button, but bot is working", "error", menuErr)
				// Не критичная ошибка, продолжаем
			}

			// Устанавливаем команды для русского языка
			_, cmdErr := b.SetMyCommands(ctx, &bot.SetMyCommandsParams{
				Commands: []models.BotCommand{
					{Command: "start", Description: "Начать работу с ботом"},
				},
				LanguageCode: "ru",
			})

			if cmdErr != nil {
				slog.Warn("Failed to set Russian commands", "error", cmdErr)
			}

			// Устанавливаем команды для английского языка
			_, cmdErr = b.SetMyCommands(ctx, &bot.SetMyCommandsParams{
				Commands: []models.BotCommand{
					{Command: "start", Description: "Start using the bot"},
				},
				LanguageCode: "en",
			})

			if cmdErr != nil {
				slog.Warn("Failed to set English commands", "error", cmdErr)
			}

			cancel()
			return me, nil
		}

		cancel()

		// Логируем ошибку
		slog.Error("Failed to initialize bot",
			"attempt", attempt,
			"error", err,
			"will_retry", attempt < maxRetries)

		// Если это не последняя попытка, ждем перед повтором
		if attempt < maxRetries {
			delay := time.Duration(attempt) * baseDelay
			slog.Info("Waiting before retry", "delay", delay)
			time.Sleep(delay)
		}
	}

	return nil, fmt.Errorf("failed to initialize bot after %d attempts", maxRetries)
}

// validateTelegramToken - проверяет формат токена Telegram бота
func validateTelegramToken(token string) error {
	if token == "" {
		return fmt.Errorf("telegram token is empty")
	}

	// Базовая проверка формата токена: должен содержать ":" и быть достаточно длинным
	if len(token) < 20 {
		return fmt.Errorf("telegram token is too short (expected at least 20 characters)")
	}

	if !strings.Contains(token, ":") {
		return fmt.Errorf("telegram token has invalid format (should contain ':')")
	}

	parts := strings.Split(token, ":")
	if len(parts) != 2 {
		return fmt.Errorf("telegram token has invalid format (should have exactly one ':')")
	}

	// Проверяем, что первая часть - это числовой ID бота
	if _, err := strconv.ParseInt(parts[0], 10, 64); err != nil {
		return fmt.Errorf("telegram token has invalid bot ID format: %w", err)
	}

	// Проверяем, что вторая часть не пустая
	if len(parts[1]) < 10 {
		return fmt.Errorf("telegram token secret part is too short")
	}

	slog.Info("Telegram token validation passed", "bot_id", parts[0])
	return nil
}

// monitorBotHealth - периодически проверяет состояние бота
func monitorBotHealth(b *bot.Bot) {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)

		_, err := b.GetMe(ctx)
		if err != nil {
			slog.Error("Bot health check failed", "error", err)
		} else {
			slog.Debug("Bot health check passed")
		}

		cancel()
	}
}
