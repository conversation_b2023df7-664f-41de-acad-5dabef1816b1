# Анализ YooKassa Recurring Payments API

## Обзор исследования

Проведено детальное исследование возможностей YooKassa API v3 для реализации автоматических рекуррентных платежей в системе автопродления подписок VPN-бота.

## 🔍 Анализ существующей интеграции

### ✅ Уже реализовано в проекте:

1. **Базовая интеграция YooKassa**
   - Клиент API в `internal/yookasa/client.go`
   - Модели данных в `internal/yookasa/models.go`
   - Создание обычных платежей через `CreateInvoice`

2. **Поддержка сохранения способов оплаты**
   - Поле `SavePaymentMethod bool` в `PaymentRequest`
   - Поле `PaymentMethodID *uuid.UUID` для использования сохраненных методов
   - Модель `SavedPaymentMethod` с полной информацией о карте

3. **Методы для работы с рекуррентными платежами**
   - ✅ `CreatePaymentWithSavedMethod` - создание платежа с сохраненным методом
   - ✅ `GetSavedPaymentMethods` - получение списка сохраненных методов
   - ✅ `CreatePaymentWithSavedMethodRequest` - модель запроса

4. **Интеграция с системой автопродления**
   - Метод `createYookasaAutoRenewalPayment` с TODO для использования сохраненных методов
   - Логика извлечения `payment_method_id` из `savedPaymentData`

## 🚀 Возможности YooKassa Recurring Payments API

### 1. Сохранение способов оплаты
- **Автоматическое сохранение**: При создании платежа с `save_payment_method: true`
- **Поддерживаемые методы**: Банковские карты, электронные кошельки
- **Срок действия**: Сохраненные методы имеют `expires_at` дату
- **Безопасность**: Соответствие стандартам PCI DSS

### 2. Автоматические списания
- **Без участия пользователя**: Платежи создаются с `payment_method_id`
- **Мгновенное списание**: При наличии средств на карте
- **Уведомления**: Webhook'и о статусе платежа
- **Повторные попытки**: Возможность автоматических повторов при ошибках

### 3. Управление сохраненными методами
- **Получение списка**: API для просмотра всех сохраненных методов клиента
- **Информация о карте**: Маскированный номер, тип карты, срок действия
- **Удаление методов**: Возможность отвязки способов оплаты

## ⚠️ Ограничения и требования

### 1. PCI DSS Compliance
- **Запрещено сохранять**: CVV код, полный номер карты, PIN
- **Разрешено сохранять**: Токенизированные данные через YooKassa
- **Требования**: Сертификация PCI DSS для обработки карточных данных

### 2. Согласие пользователя
- **Обязательно**: Явное согласие на сохранение способа оплаты
- **Информирование**: Уведомление о том, что данные будут сохранены
- **Отзыв согласия**: Возможность удаления сохраненных методов

### 3. Обработка ошибок
- **Недостаток средств**: Платеж может быть отклонен банком
- **Заблокированная карта**: Временная или постоянная блокировка
- **Истекший срок**: Карта может стать недействительной
- **Технические ошибки**: Проблемы на стороне банка или платежной системы

## 🔧 Необходимые изменения в Go SDK

### 1. Обновление extractPaymentMethodData
**Файл**: `internal/payment/payment.go`

**Текущее состояние**: Метод не сохраняет `payment_method_id` из YooKassa платежей

**Требуемые изменения**:
```go
// Добавить извлечение payment_method_id из успешного платежа YooKassa
if paymentData.PaymentMethod.ID != uuid.Nil {
    data["payment_method_id"] = paymentData.PaymentMethod.ID.String()
    data["payment_type"] = paymentData.PaymentMethod.Type
    data["card_mask"] = extractCardMask(paymentData.PaymentMethod.Card)
    data["expires_at"] = paymentData.PaymentMethod.ExpiresAt
}
```

### 2. Реализация автоматических списаний
**Файл**: `internal/payment/payment.go`

**Текущее состояние**: TODO в методе `createYookasaAutoRenewalPayment`

**Требуемые изменения**:
```go
func (ps *PaymentService) createYookasaAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff, savedPaymentData map[string]any) (int64, error) {
    paymentMethodID, ok := savedPaymentData["payment_method_id"].(string)
    if !ok || paymentMethodID == "" {
        return ps.createYookasaInvoiceWithSaveMethod(ctx, customer, tariff)
    }
    
    return ps.createAutomaticYookasaPayment(ctx, customer, tariff, paymentMethodID)
}
```

### 3. Добавление обработки ошибок и повторных попыток
**Новые методы**:
- `handleYookasaPaymentError` - анализ типа ошибки
- `scheduleRetryPayment` - планирование повторной попытки
- `notifyPaymentFailure` - уведомление пользователя об ошибке

### 4. Интеграция с системой уведомлений
**Файл**: `internal/service/auto_renewal.go`

**Добавить**:
- Уведомления об успешном автоматическом списании
- Уведомления об ошибках с предложением обновить способ оплаты
- Напоминания о необходимости добавить новую карту

## 📊 Рекомендуемая архитектура реализации

### Этап 1: Базовая функциональность
1. ✅ Обновление `extractPaymentMethodData` для сохранения `payment_method_id`
2. ✅ Реализация автоматических списаний в `createYookasaAutoRenewalPayment`
3. ✅ Добавление создания платежей с `save_payment_method: true`

### Этап 2: Обработка ошибок
1. Реализация анализа ошибок YooKassa API
2. Добавление логики повторных попыток
3. Интеграция с системой уведомлений

### Этап 3: Управление способами оплаты
1. API для просмотра сохраненных карт пользователем
2. Возможность удаления способов оплаты
3. Обновление истекших карт

## 🔒 Требования безопасности

### 1. Хранение данных
- **Использовать**: Только токены YooKassa (`payment_method_id`)
- **Не хранить**: CVV, полные номера карт, PIN-коды
- **Шифрование**: Дополнительное шифрование токенов в базе данных

### 2. Логирование
- **Маскировать**: Номера карт в логах (показывать только первые 6 и последние 4 цифры)
- **Не логировать**: CVV, полные номера карт, персональные данные
- **Аудит**: Ведение журнала всех операций с сохраненными методами

### 3. Согласие пользователя
- **Чекбокс**: Явное согласие на сохранение способа оплаты
- **Информирование**: Объяснение преимуществ автоматических платежей
- **Отзыв**: Простая процедура удаления сохраненных данных

## 📈 Ожидаемые результаты

### Преимущества для пользователей:
- ✅ Автоматическое продление без участия пользователя
- ✅ Отсутствие перерывов в обслуживании
- ✅ Удобство использования

### Преимущества для бизнеса:
- ✅ Увеличение retention rate
- ✅ Снижение churn rate
- ✅ Автоматизация процесса оплаты
- ✅ Улучшение cash flow

### Технические преимущества:
- ✅ Снижение нагрузки на поддержку
- ✅ Автоматизация процессов
- ✅ Улучшение пользовательского опыта

## 🎯 Критерии успешной реализации

1. **Функциональность**:
   - ✅ Сохранение способов оплаты при первом платеже
   - ✅ Автоматические списания без участия пользователя
   - ✅ Корректная обработка ошибок и повторные попытки

2. **Безопасность**:
   - ✅ Соответствие требованиям PCI DSS
   - ✅ Безопасное хранение токенов
   - ✅ Правильное логирование без утечки данных

3. **Пользовательский опыт**:
   - ✅ Понятные уведомления об автоматических платежах
   - ✅ Простое управление сохраненными способами оплаты
   - ✅ Прозрачность процесса автопродления

## 🚀 Готовность к реализации

**Статус**: ✅ ГОТОВ К РЕАЛИЗАЦИИ

**Основание**: 
- Существующий Go SDK уже содержит все необходимые методы
- Архитектура проекта поддерживает интеграцию рекуррентных платежей
- Документация YooKassa предоставляет полную информацию для реализации

**Следующий шаг**: Переход к реализации обновления структуры данных и автоматических списаний.
