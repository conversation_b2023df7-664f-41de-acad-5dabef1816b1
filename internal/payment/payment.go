package payment

import (
	"context"
	"fmt"
	"log/slog"
	"math"
	"remnawave-tg-shop-bot/internal/cache"
	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/cryptopay"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/remnawave"
	"remnawave-tg-shop-bot/internal/service"
	"remnawave-tg-shop-bot/internal/translation"
	"remnawave-tg-shop-bot/internal/yookasa"
	"remnawave-tg-shop-bot/utils"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
	"github.com/google/uuid"
)

// AutoRenewalInterface - интерфейс для управления автопродлением
type AutoRenewalInterface interface {
	EnableAutoRenewalAfterPayment(ctx context.Context, customerID int64, paymentMethod string, tariffCode string, paymentMethodData map[string]any) error
	IsAutoRenewalEnabled(ctx context.Context, customerID int64) (bool, error)
}

type PaymentService struct {
	purchaseRepository    *database.PurchaseRepository
	remnawaveClient       *remnawave.Client
	customerRepository    *database.CustomerRepository
	telegramBot           *bot.Bot
	translation           *translation.Manager
	cryptoPayClient       *cryptopay.Client
	yookasaClient         *yookasa.Client
	referralRepository    *database.ReferralRepository
	cache                 *cache.Cache
	promoCodeService      *service.PromoCodeService
	tariffRepository      *database.TariffRepository
	autoRenewalInterface  AutoRenewalInterface
	autoRenewalRepository *database.AutoRenewalRepository
}

func NewPaymentService(
	translation *translation.Manager,
	purchaseRepository *database.PurchaseRepository,
	remnawaveClient *remnawave.Client,
	customerRepository *database.CustomerRepository,
	telegramBot *bot.Bot,
	cryptoPayClient *cryptopay.Client,
	yookasaClient *yookasa.Client,
	referralRepository *database.ReferralRepository,
	cache *cache.Cache,
	promoCodeService *service.PromoCodeService,
	tariffRepository *database.TariffRepository,
) *PaymentService {
	return &PaymentService{
		purchaseRepository: purchaseRepository,
		remnawaveClient:    remnawaveClient,
		customerRepository: customerRepository,
		telegramBot:        telegramBot,
		translation:        translation,
		cryptoPayClient:    cryptoPayClient,
		yookasaClient:      yookasaClient,
		referralRepository: referralRepository,
		cache:              cache,
		promoCodeService:   promoCodeService,
		tariffRepository:   tariffRepository,
	}
}

// SetAutoRenewalInterface - устанавливает интерфейс для управления автопродлением
func (ps *PaymentService) SetAutoRenewalInterface(autoRenewalInterface AutoRenewalInterface) {
	ps.autoRenewalInterface = autoRenewalInterface
}

// SetAutoRenewalRepository - устанавливает репозиторий для автопродления
func (ps *PaymentService) SetAutoRenewalRepository(autoRenewalRepository *database.AutoRenewalRepository) {
	ps.autoRenewalRepository = autoRenewalRepository
}

// EnableTelegramStarsSubscription - включает автопродление подписки Telegram Stars
func (ps *PaymentService) EnableTelegramStarsSubscription(ctx context.Context, customerID int64) error {
	// Получаем настройки автопродления
	autoRenewalSettings, err := ps.autoRenewalRepository.GetByCustomerID(ctx, customerID)
	if err != nil {
		slog.Error("Failed to get auto renewal settings for subscription enable", "error", err, "customer_id", customerID)
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	if autoRenewalSettings == nil {
		return fmt.Errorf("auto renewal settings not found for customer %d", customerID)
	}

	// Извлекаем telegram_payment_charge_id из сохраненных данных
	telegramPaymentChargeID, ok := autoRenewalSettings.SavedPaymentData["telegram_payment_charge_id"].(string)
	if !ok || telegramPaymentChargeID == "" {
		return fmt.Errorf("telegram_payment_charge_id not found in saved payment data for customer %d", customerID)
	}

	// Вызываем API для включения подписки (IsCanceled = false)
	success, err := ps.telegramBot.EditUserStarSubscription(ctx, &bot.EditUserStarSubscriptionParams{
		UserID:                  customerID,
		TelegramPaymentChargeID: telegramPaymentChargeID,
		IsCanceled:              false,
	})

	if err != nil {
		slog.Error("Failed to enable Telegram Stars subscription", "error", err, "customer_id", customerID, "charge_id", telegramPaymentChargeID)
		return fmt.Errorf("failed to enable subscription: %w", err)
	}

	if !success {
		slog.Error("Telegram API returned false for subscription enable", "customer_id", customerID, "charge_id", telegramPaymentChargeID)
		return fmt.Errorf("failed to enable subscription: API returned false")
	}

	slog.Info("Telegram Stars subscription enabled successfully", "customer_id", customerID, "charge_id", telegramPaymentChargeID)
	return nil
}

// DisableTelegramStarsSubscription - отключает автопродление подписки Telegram Stars
func (ps *PaymentService) DisableTelegramStarsSubscription(ctx context.Context, customerID int64) error {
	// Получаем настройки автопродления
	autoRenewalSettings, err := ps.autoRenewalRepository.GetByCustomerID(ctx, customerID)
	if err != nil {
		slog.Error("Failed to get auto renewal settings for subscription disable", "error", err, "customer_id", customerID)
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	if autoRenewalSettings == nil {
		return fmt.Errorf("auto renewal settings not found for customer %d", customerID)
	}

	// Извлекаем telegram_payment_charge_id из сохраненных данных
	telegramPaymentChargeID, ok := autoRenewalSettings.SavedPaymentData["telegram_payment_charge_id"].(string)
	if !ok || telegramPaymentChargeID == "" {
		return fmt.Errorf("telegram_payment_charge_id not found in saved payment data for customer %d", customerID)
	}

	// Вызываем API для отключения подписки (IsCanceled = true)
	success, err := ps.telegramBot.EditUserStarSubscription(ctx, &bot.EditUserStarSubscriptionParams{
		UserID:                  customerID,
		TelegramPaymentChargeID: telegramPaymentChargeID,
		IsCanceled:              true,
	})

	if err != nil {
		slog.Error("Failed to disable Telegram Stars subscription", "error", err, "customer_id", customerID, "charge_id", telegramPaymentChargeID)
		return fmt.Errorf("failed to disable subscription: %w", err)
	}

	if !success {
		slog.Error("Telegram API returned false for subscription disable", "customer_id", customerID, "charge_id", telegramPaymentChargeID)
		return fmt.Errorf("failed to disable subscription: API returned false")
	}

	slog.Info("Telegram Stars subscription disabled successfully", "customer_id", customerID, "charge_id", telegramPaymentChargeID)
	return nil
}

// ProcessPurchaseByIdWithSubscriptionData - обрабатывает покупку с дополнительными данными подписки
func (s PaymentService) ProcessPurchaseByIdWithSubscriptionData(ctx context.Context, purchaseId int64, subscriptionData map[string]any) error {
	// Сначала обрабатываем покупку стандартным способом
	err := s.ProcessPurchaseById(ctx, purchaseId)
	if err != nil {
		return err
	}

	// Если есть данные подписки (для Telegram Stars), сохраняем их
	if len(subscriptionData) > 0 {
		purchase, err := s.purchaseRepository.FindById(ctx, purchaseId)
		if err != nil {
			slog.Error("Error finding purchase for subscription data update", "error", err, "purchase_id", purchaseId)
			return err
		}

		customer, err := s.customerRepository.FindById(ctx, purchase.CustomerID)
		if err != nil {
			slog.Error("Error finding customer for subscription data update", "error", err, "customer_id", purchase.CustomerID)
			return err
		}

		// Обновляем данные подписки через метод UpdateTelegramStarsSubscriptionData
		err = s.UpdateTelegramStarsSubscriptionData(ctx, customer.ID, subscriptionData)
		if err != nil {
			slog.Error("Error updating Telegram Stars subscription data", "error", err, "customer_id", customer.ID)
			// Не возвращаем ошибку, так как основная покупка уже обработана
		}
	}

	return nil
}

func (s PaymentService) ProcessPurchaseById(ctx context.Context, purchaseId int64) error {
	purchase, err := s.purchaseRepository.FindById(ctx, purchaseId)
	if err != nil {
		return err
	}
	if purchase == nil {
		return fmt.Errorf("purchase with crypto invoice id %s not found", utils.MaskHalfInt64(purchaseId))
	}

	customer, err := s.customerRepository.FindById(ctx, purchase.CustomerID)
	if err != nil {
		return err
	}
	if customer == nil {
		return fmt.Errorf("customer %s not found", utils.MaskHalfInt64(purchase.CustomerID))
	}

	if messageId, b := s.cache.GetInt(purchase.ID); b {
		_, err = s.telegramBot.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    customer.TelegramID,
			MessageID: messageId,
		})
		if err != nil {
			slog.Error("Error deleting message", "error", err)
		}
	}

	user, err := s.remnawaveClient.CreateOrUpdateUser(ctx, customer.ID, customer.TelegramID, config.TrafficLimit(), purchase.Month*30)
	if err != nil {
		return err
	}

	err = s.purchaseRepository.MarkAsPaid(ctx, purchase.ID)
	if err != nil {
		return err
	}

	customerFilesToUpdate := map[string]interface{}{
		"subscription_link": user.SubscriptionUrl,
		"expire_at":         user.ExpireAt,
	}

	err = s.customerRepository.UpdateFields(ctx, customer.ID, customerFilesToUpdate)
	if err != nil {
		return err
	}

	// Применяем промокод, если он был указан при создании покупки
	if purchase.PromoCode != nil && *purchase.PromoCode != "" && purchase.TariffCode != nil && *purchase.TariffCode != "" {
		err = s.applyPromoCodeToPurchase(ctx, purchase, customer)
		if err != nil {
			slog.Error("Ошибка применения промокода к оплаченной покупке", "error", err, "purchaseId", purchase.ID, "promoCode", *purchase.PromoCode)
			// Не возвращаем ошибку, так как покупка уже обработана
		}
	}

	// Автоматически включаем автопродление после успешной оплаты
	if purchase.TariffCode != nil && *purchase.TariffCode != "" {
		paymentMethod := s.getPaymentMethodFromPurchase(purchase)
		paymentMethodData := s.extractPaymentMethodData(purchase, paymentMethod)

		err = s.EnableAutoRenewalAfterPayment(ctx, customer.ID, paymentMethod, *purchase.TariffCode, paymentMethodData)
		if err != nil {
			slog.Error("Ошибка включения автопродления после оплаты", "error", err, "purchaseId", purchase.ID, "paymentMethod", paymentMethod)
			// Не возвращаем ошибку, так как покупка уже обработана
		}
	}

	// Обновляем объект customer с новыми данными перед созданием сообщения
	customer.SubscriptionLink = &user.SubscriptionUrl
	customer.ExpireAt = &user.ExpireAt

	// Создаем улучшенное сообщение с временем окончания подписки
	subscriptionText := s.createSubscriptionActivatedMessageDetailed(customer, purchase)

	// Пытаемся отредактировать исходное сообщение с кнопками оплаты, если есть message_id
	if purchase.MessageID != nil {
		_, err = s.telegramBot.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    customer.TelegramID,
			MessageID: int(*purchase.MessageID),
			Text:      subscriptionText,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: s.createConnectKeyboard(customer),
			},
		})
		if err != nil {
			slog.Error("Error editing payment message, sending new message instead", "error", err)
			// Если редактирование не удалось, отправляем новое сообщение с retry
			err = s.retryTelegramAPI(ctx, func() error {
				_, retryErr := s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
					ChatID:    customer.TelegramID,
					Text:      subscriptionText,
					ParseMode: models.ParseModeHTML,
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: s.createConnectKeyboard(customer),
					},
				})
				return retryErr
			}, "send_subscription_message_fallback")
			if err != nil {
				return err
			}
		}
	} else {
		// Если message_id нет, отправляем новое сообщение как раньше с retry
		err = s.retryTelegramAPI(ctx, func() error {
			_, retryErr := s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
				ChatID:    customer.TelegramID,
				Text:      subscriptionText,
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: s.createConnectKeyboard(customer),
				},
			})
			return retryErr
		}, "send_subscription_message")
		if err != nil {
			return err
		}
	}

	ctxReferee := context.Background()
	referee, err := s.referralRepository.FindByReferee(ctxReferee, customer.TelegramID)
	if referee == nil {
		return nil
	}
	if referee.BonusGranted {
		return nil
	}
	if err != nil {
		return err
	}
	refereeCustomer, err := s.customerRepository.FindByTelegramId(ctxReferee, referee.ReferrerID)
	if err != nil {
		return err
	}
	refereeUser, err := s.remnawaveClient.CreateOrUpdateUser(ctxReferee, refereeCustomer.ID, refereeCustomer.TelegramID, config.TrafficLimit(), config.GetReferralDays())
	if err != nil {
		return err
	}
	refereeUserFilesToUpdate := map[string]interface{}{
		"subscription_link": refereeUser.GetSubscriptionUrl(),
		"expire_at":         refereeUser.GetExpireAt(),
	}
	err = s.customerRepository.UpdateFields(ctxReferee, refereeCustomer.ID, refereeUserFilesToUpdate)
	if err != nil {
		return err
	}
	err = s.referralRepository.MarkBonusGranted(ctxReferee, referee.ID)
	if err != nil {
		return err
	}
	slog.Info("Granted referral bonus", "customer_id", utils.MaskHalfInt64(refereeCustomer.ID))
	err = s.retryTelegramAPI(ctxReferee, func() error {
		_, retryErr := s.telegramBot.SendMessage(ctxReferee, &bot.SendMessageParams{
			ChatID:    refereeCustomer.TelegramID,
			ParseMode: models.ParseModeHTML,
			Text:      s.translation.GetText(refereeCustomer.Language, "referral_bonus_granted"),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: s.createConnectKeyboard(refereeCustomer),
			},
		})
		return retryErr
	}, "send_referral_bonus_message")
	if err != nil {
		slog.Error("Failed to send referral bonus message after retries", "error", err, "customer_id", refereeCustomer.ID)
	}
	slog.Info("purchase processed", "purchase_id", utils.MaskHalfInt64(purchase.ID), "type", purchase.InvoiceType, "customer_id", utils.MaskHalfInt64(customer.ID))

	return nil
}

func (s PaymentService) createConnectKeyboard(customer *database.Customer) [][]models.InlineKeyboardButton {
	var inlineCustomerKeyboard [][]models.InlineKeyboardButton
	var url string
	if customer != nil && customer.SubscriptionLink != nil && *customer.SubscriptionLink != "" {
		url = *customer.SubscriptionLink
	} else if config.GetMiniAppURL() != "" {
		url = config.GetMiniAppURL()
	}
	if url != "" {
		inlineCustomerKeyboard = append(inlineCustomerKeyboard, []models.InlineKeyboardButton{
			{Text: s.translation.GetText(customer.Language, "connect_button"), WebApp: &models.WebAppInfo{URL: url}},
		})
	} else {
		inlineCustomerKeyboard = append(inlineCustomerKeyboard, []models.InlineKeyboardButton{
			{Text: s.translation.GetText(customer.Language, "connect_button"), CallbackData: "connect"},
		})
	}
	inlineCustomerKeyboard = append(inlineCustomerKeyboard, []models.InlineKeyboardButton{
		{Text: s.translation.GetText(customer.Language, "back_button"), CallbackData: "start"},
	})
	return inlineCustomerKeyboard
}

// createSubscriptionActivatedMessage - создает сообщение об активации подписки с временем окончания
func (s PaymentService) createSubscriptionActivatedMessage(customer *database.Customer) string {
	baseMessage := s.translation.GetText(customer.Language, "subscription_activated")

	// Добавляем информацию о времени окончания подписки, если она есть
	if customer.ExpireAt != nil {
		// Используем правильное форматирование времени как в системе рассылок
		loc, err := time.LoadLocation(config.TimeZone())
		if err != nil {
			loc = time.FixedZone("UTC+3", 3*60*60) // fallback
		}

		expireTimeFormatted := customer.ExpireAt.In(loc)
		_, offset := expireTimeFormatted.Zone()
		hours := offset / 3600
		var sign string
		if hours >= 0 {
			sign = "+"
		} else {
			sign = "-"
		}

		timeStr := expireTimeFormatted.Format("02.01.2006 15:04") +
			fmt.Sprintf(" (UTC%s%02d)", sign, abs(hours))

		baseMessage += fmt.Sprintf("\n\n⏰ <b>Подписка действует до:</b> %s", timeStr)
	}

	return baseMessage
}

// createSubscriptionActivatedMessageDetailed - создает детальное сообщение об активации подписки
func (s PaymentService) createSubscriptionActivatedMessageDetailed(customer *database.Customer, purchase *database.Purchase) string {
	langCode := customer.Language
	if langCode == "" {
		langCode = "ru"
	}

	// Получаем информацию о тарифе
	var tariffTitle string
	var price int
	var currency string
	var paymentMethodName string

	if purchase.TariffCode != nil && *purchase.TariffCode != "" {
		// Получаем тариф по коду
		ctx := context.Background()
		tariff, err := s.tariffRepository.GetByCode(ctx, *purchase.TariffCode)
		if err == nil && tariff != nil {
			tariffTitle = tariff.Title
			switch purchase.InvoiceType {
			case database.InvoiceTypeTelegram:
				price = tariff.PriceStars
				currency = "⭐"
			default:
				price = tariff.PriceRUB
				currency = "₽"
			}
		} else {
			tariffTitle = "Подписка"
			price = int(purchase.Amount)
			if purchase.Currency == "STARS" {
				currency = "⭐"
			} else {
				currency = "₽"
			}
		}
	} else {
		tariffTitle = "Подписка"
		price = int(purchase.Amount)
		if purchase.Currency == "STARS" {
			currency = "⭐"
		} else {
			currency = "₽"
		}
	}

	// Определяем название способа оплаты
	switch purchase.InvoiceType {
	case database.InvoiceTypeYookasa:
		paymentMethodName = "Банковская карта"
	case database.InvoiceTypeTelegram:
		paymentMethodName = "Telegram Stars"
	case database.InvoiceTypeCrypto:
		paymentMethodName = "Криптовалюта"
	case database.InvoiceTypeTribute:
		paymentMethodName = "Tribute"
	default:
		paymentMethodName = "Неизвестный способ"
	}

	// Форматируем время окончания подписки
	var expireTimeStr string
	if customer.ExpireAt != nil {
		loc, err := time.LoadLocation(config.TimeZone())
		if err != nil {
			loc = time.FixedZone("UTC+3", 3*60*60) // fallback
		}

		expireTimeFormatted := customer.ExpireAt.In(loc)
		_, offset := expireTimeFormatted.Zone()
		hours := offset / 3600
		var sign string
		if hours >= 0 {
			sign = "+"
		} else {
			sign = "-"
		}

		expireTimeStr = expireTimeFormatted.Format("02.01.2006 15:04") +
			fmt.Sprintf(" (UTC%s%02d)", sign, abs(hours))
	} else {
		expireTimeStr = "Не определено"
	}

	// Определяем статус автопродления
	autoRenewalStatus := s.translation.GetText(langCode, "auto_renewal_disabled_short")
	if s.autoRenewalInterface != nil {
		ctx := context.Background()
		isEnabled, err := s.autoRenewalInterface.IsAutoRenewalEnabled(ctx, customer.ID)
		if err == nil && isEnabled {
			autoRenewalStatus = s.translation.GetText(langCode, "auto_renewal_enabled_short")
		}
	}

	// Создаем детальное сообщение
	return fmt.Sprintf(
		s.translation.GetText(langCode, "subscription_activated_detailed"),
		tariffTitle,
		price,
		currency,
		expireTimeStr,
		autoRenewalStatus,
		paymentMethodName,
	)
}

// abs - вспомогательная функция для получения абсолютного значения
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func (s PaymentService) CreatePurchase(ctx context.Context, amount int, months int, customer *database.Customer, invoiceType database.InvoiceType) (url string, purchaseId int64, err error) {
	// Метод CreatePurchase не поддерживает промокоды и тарифы, передаем пустые строки
	switch invoiceType {
	case database.InvoiceTypeCrypto:
		return s.createCryptoInvoice(ctx, amount, months, customer, "", "")
	case database.InvoiceTypeYookasa:
		return s.createYookasaInvoice(ctx, amount, months, customer, "", "")
	case database.InvoiceTypeTelegram:
		return s.createTelegramInvoice(ctx, amount, months, customer, "", "")
	case database.InvoiceTypeTribute:
		return s.createTributeInvoice(ctx, amount, months, customer, "", "")
	default:
		return "", 0, fmt.Errorf("unknown invoice type: %s", invoiceType)
	}
}

func (s PaymentService) CreatePurchaseByTariff(ctx context.Context, tariff *database.Tariff, customer *database.Customer, invoiceType database.InvoiceType, promoCode string) (url string, purchaseId int64, err error) {
	var amount int
	// Извлекаем количество месяцев из кода тарифа
	months := tariff.GetMonthsFromCode()

	// Определяем базовую сумму в зависимости от типа оплаты
	var originalAmount int
	var currency string
	switch invoiceType {
	case database.InvoiceTypeTelegram:
		originalAmount = tariff.PriceStars
		currency = "STARS"
	case database.InvoiceTypeYookasa, database.InvoiceTypeCrypto, database.InvoiceTypeTribute:
		originalAmount = tariff.PriceRUB
		currency = "RUB"
	default:
		return "", 0, fmt.Errorf("неизвестный тип оплаты: %s", invoiceType)
	}

	// Применяем промокод, если он указан
	amount = originalAmount
	var validPromoCode *database.PromoCode
	if promoCode != "" && s.promoCodeService != nil {
		// Валидируем промокод
		validatedPromoCode, err := s.promoCodeService.ValidatePromoCode(ctx, promoCode, customer, tariff)
		if err != nil {
			slog.Error("Ошибка валидации промокода", "error", err, "code", promoCode)
			// Продолжаем без промокода, если валидация не прошла
		} else {
			validPromoCode = validatedPromoCode
			// Рассчитываем цену со скидкой
			_, amount = s.promoCodeService.CalculateDiscountedPrice(originalAmount, validPromoCode.DiscountPercent, currency)
		}
	}

	switch invoiceType {
	case database.InvoiceTypeCrypto:
		url, purchaseId, err = s.createCryptoInvoice(ctx, amount, months, customer, promoCode, tariff.Code)
	case database.InvoiceTypeYookasa:
		url, purchaseId, err = s.createYookasaInvoice(ctx, amount, months, customer, promoCode, tariff.Code)
	case database.InvoiceTypeTelegram:
		url, purchaseId, err = s.createTelegramInvoice(ctx, amount, months, customer, promoCode, tariff.Code)
	case database.InvoiceTypeTribute:
		url, purchaseId, err = s.createTributeInvoice(ctx, amount, months, customer, promoCode, tariff.Code)
	default:
		return "", 0, fmt.Errorf("неизвестный тип оплаты: %s", invoiceType)
	}

	if err != nil {
		return "", 0, err
	}

	// TODO: Применение промокода должно происходить только после успешной оплаты
	// Информация о промокоде должна сохраняться в purchase или передаваться через payload

	return url, purchaseId, nil
}

// CreatePurchaseByTariffWithMessageID - создает покупку с сохранением ID сообщения для последующего редактирования
func (s PaymentService) CreatePurchaseByTariffWithMessageID(ctx context.Context, tariff *database.Tariff, customer *database.Customer, invoiceType database.InvoiceType, promoCode string, messageID int) (url string, purchaseId int64, err error) {
	var amount int
	// Извлекаем количество месяцев из кода тарифа
	months := tariff.GetMonthsFromCode()

	// Определяем базовую сумму в зависимости от типа оплаты
	var originalAmount int
	var currency string
	switch invoiceType {
	case database.InvoiceTypeTelegram:
		originalAmount = tariff.PriceStars
		currency = "STARS"
	case database.InvoiceTypeYookasa, database.InvoiceTypeCrypto, database.InvoiceTypeTribute:
		originalAmount = tariff.PriceRUB
		currency = "RUB"
	default:
		return "", 0, fmt.Errorf("неизвестный тип оплаты: %s", invoiceType)
	}

	// Применяем промокод, если он указан
	amount = originalAmount
	var validPromoCode *database.PromoCode
	if promoCode != "" && s.promoCodeService != nil {
		// Валидируем промокод
		validatedPromoCode, err := s.promoCodeService.ValidatePromoCode(ctx, promoCode, customer, tariff)
		if err != nil {
			slog.Error("Ошибка валидации промокода", "error", err, "code", promoCode)
			// Продолжаем без промокода, если валидация не прошла
		} else {
			validPromoCode = validatedPromoCode
			// Рассчитываем цену со скидкой
			_, amount = s.promoCodeService.CalculateDiscountedPrice(originalAmount, validPromoCode.DiscountPercent, currency)
		}
	}

	messageIDPtr := int64(messageID)

	switch invoiceType {
	case database.InvoiceTypeCrypto:
		url, purchaseId, err = s.createCryptoInvoiceWithMessageID(ctx, amount, months, customer, promoCode, tariff.Code, &messageIDPtr)
	case database.InvoiceTypeYookasa:
		url, purchaseId, err = s.createYookasaInvoiceWithMessageID(ctx, amount, months, customer, promoCode, tariff.Code, &messageIDPtr)
	case database.InvoiceTypeTelegram:
		url, purchaseId, err = s.createTelegramInvoiceWithMessageID(ctx, amount, months, customer, promoCode, tariff.Code, &messageIDPtr)
	case database.InvoiceTypeTribute:
		url, purchaseId, err = s.createTributeInvoiceWithMessageID(ctx, amount, months, customer, promoCode, tariff.Code, &messageIDPtr)
	default:
		return "", 0, fmt.Errorf("неизвестный тип оплаты: %s", invoiceType)
	}

	if err != nil {
		return "", 0, err
	}

	return url, purchaseId, nil
}

func (s PaymentService) createCryptoInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeCrypto,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", "error", err)
		return "", 0, err
	}

	invoice, err := s.cryptoPayClient.CreateInvoice(&cryptopay.InvoiceRequest{
		CurrencyType:   "fiat",
		Fiat:           "RUB",
		Amount:         fmt.Sprintf("%d", amount),
		AcceptedAssets: "USDT",
		Payload:        fmt.Sprintf("purchaseId=%d&username=%s", purchaseId, ctx.Value("username")),
		Description: fmt.Sprintf("Subscription on %d month%s", months, func() string {
			if months > 1 {
				return "s"
			} else {
				return ""
			}
		}()),
		PaidBtnName: "callback",
		PaidBtnUrl:  config.BotURL(),
	})
	if err != nil {
		slog.Error("Error creating invoice", "error", err)
		return "", 0, err
	}

	updates := map[string]interface{}{
		"crypto_invoice_url": invoice.BotInvoiceUrl,
		"crypto_invoice_id":  invoice.InvoiceID,
		"status":             database.PurchaseStatusPending,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, updates)
	if err != nil {
		slog.Error("Error updating purchase", "error", err)
		return "", 0, err
	}

	return invoice.BotInvoiceUrl, purchaseId, nil
}

func (s PaymentService) createYookasaInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeYookasa,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", "error", err)
		return "", 0, err
	}

	invoice, err := s.yookasaClient.CreateInvoice(ctx, amount, months, customer.ID, purchaseId)
	if err != nil {
		slog.Error("Error creating invoice", "error", err)
		return "", 0, err
	}

	updates := map[string]interface{}{
		"yookasa_url": invoice.Confirmation.ConfirmationURL,
		"yookasa_id":  invoice.ID,
		"status":      database.PurchaseStatusPending,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, updates)
	if err != nil {
		slog.Error("Error updating purchase", "error", err)
		return "", 0, err
	}

	return invoice.Confirmation.ConfirmationURL, purchaseId, nil
}

func (s PaymentService) createTelegramInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeTelegram,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "STARS",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", "error", err)
		return "", 0, nil
	}

	// Рассчитываем период подписки в секундах (месяцы * 30 дней * 24 часа * 60 минут * 60 секунд)
	subscriptionPeriodSeconds := months * 30 * 24 * 60 * 60

	invoiceUrl, err := s.telegramBot.CreateInvoiceLink(ctx, &bot.CreateInvoiceLinkParams{
		Title:    s.translation.GetText(customer.Language, "invoice_title"),
		Currency: "XTR",
		Prices: []models.LabeledPrice{
			{
				Label:  s.translation.GetText(customer.Language, "invoice_label"),
				Amount: amount,
			},
		},
		Description:        s.translation.GetText(customer.Language, "invoice_description"),
		Payload:            fmt.Sprintf("%d&%s", purchaseId, ctx.Value("username")),
		SubscriptionPeriod: subscriptionPeriodSeconds,
	})

	updates := map[string]interface{}{
		"status": database.PurchaseStatusPending,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, updates)
	if err != nil {
		slog.Error("Error updating purchase", "error", err)
		return "", 0, err
	}

	return invoiceUrl, purchaseId, nil
}

func (s PaymentService) ActivateTrial(ctx context.Context, telegramId int64) (string, error) {
	if config.TrialDays() == 0 {
		return "", nil
	}
	customer, err := s.customerRepository.FindByTelegramId(ctx, telegramId)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return "", err
	}
	if customer == nil {
		return "", fmt.Errorf("customer %d not found", telegramId)
	}
	user, err := s.remnawaveClient.CreateOrUpdateUser(ctx, customer.ID, telegramId, config.TrialTrafficLimit(), config.TrialDays())
	if err != nil {
		slog.Error("Error creating user", "error", err)
		return "", err
	}

	customerFilesToUpdate := map[string]interface{}{
		"subscription_link": user.GetSubscriptionUrl(),
		"expire_at":         user.GetExpireAt(),
	}

	err = s.customerRepository.UpdateFields(ctx, customer.ID, customerFilesToUpdate)
	if err != nil {
		return "", err
	}

	return user.GetSubscriptionUrl(), nil

}

func (s PaymentService) CancelPayment(purchaseId int64) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	purchase, err := s.purchaseRepository.FindById(ctx, purchaseId)
	if err != nil {
		return err
	}
	if purchase == nil {
		return fmt.Errorf("purchase with crypto invoice id %s not found", utils.MaskHalfInt64(purchaseId))
	}

	purchaseFieldsToUpdate := map[string]interface{}{
		"status": database.PurchaseStatusCancel,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, purchaseFieldsToUpdate)
	if err != nil {
		return err
	}

	return nil
}

// applyPromoCodeToPurchase применяет промокод к оплаченной покупке
func (s PaymentService) applyPromoCodeToPurchase(ctx context.Context, purchase *database.Purchase, customer *database.Customer) error {
	if s.promoCodeService == nil {
		return fmt.Errorf("promoCodeService не инициализирован")
	}

	// Получаем промокод из базы данных
	promoCode, err := s.promoCodeService.GetPromoCodeByCode(ctx, *purchase.PromoCode)
	if err != nil {
		return fmt.Errorf("ошибка получения промокода: %w", err)
	}
	if promoCode == nil {
		return fmt.Errorf("промокод %s не найден", *purchase.PromoCode)
	}

	// Получаем тариф из базы данных
	tariff, err := s.tariffRepository.GetByCode(ctx, *purchase.TariffCode)
	if err != nil {
		return fmt.Errorf("ошибка получения тарифа: %w", err)
	}
	if tariff == nil {
		return fmt.Errorf("тариф %s не найден", *purchase.TariffCode)
	}

	// Определяем валюту на основе типа инвойса
	var currency string
	switch purchase.InvoiceType {
	case database.InvoiceTypeTelegram:
		currency = "STARS"
	case database.InvoiceTypeYookasa, database.InvoiceTypeCrypto, database.InvoiceTypeTribute:
		currency = "RUB"
	default:
		return fmt.Errorf("неизвестный тип инвойса: %s", purchase.InvoiceType)
	}

	// Применяем промокод
	_, err = s.promoCodeService.ApplyPromoCode(ctx, promoCode, customer, tariff, purchase.ID, currency)
	if err != nil {
		return fmt.Errorf("ошибка применения промокода: %w", err)
	}

	slog.Info("Промокод успешно применен к покупке", "purchaseId", purchase.ID, "promoCode", *purchase.PromoCode, "customerId", customer.ID)
	return nil
}

func (s PaymentService) createTributeInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeTribute,
		Status:      database.PurchaseStatusPending,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", "error", err)
		return "", 0, err
	}

	return "", purchaseId, nil
}

// Методы создания инвойсов с сохранением MessageID для последующего редактирования

func (s PaymentService) createCryptoInvoiceWithMessageID(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string, messageID *int64) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeCrypto,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
		MessageID:   messageID,
	})
	if err != nil {
		slog.Error("Error creating purchase", "error", err)
		return "", 0, err
	}

	invoice, err := s.cryptoPayClient.CreateInvoice(&cryptopay.InvoiceRequest{
		CurrencyType:   "fiat",
		Fiat:           "RUB",
		Amount:         fmt.Sprintf("%d", amount),
		AcceptedAssets: "USDT",
		Payload:        fmt.Sprintf("purchaseId=%d&username=%s", purchaseId, ctx.Value("username")),
		Description: fmt.Sprintf("Subscription on %d month%s", months, func() string {
			if months > 1 {
				return "s"
			} else {
				return ""
			}
		}()),
		PaidBtnName: "callback",
		PaidBtnUrl:  config.BotURL(),
	})
	if err != nil {
		slog.Error("Error creating invoice", "error", err)
		return "", 0, err
	}

	updates := map[string]interface{}{
		"crypto_invoice_url": invoice.BotInvoiceUrl,
		"crypto_invoice_id":  invoice.InvoiceID,
		"status":             database.PurchaseStatusPending,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, updates)
	if err != nil {
		slog.Error("Error updating purchase", "error", err)
		return "", 0, err
	}

	return invoice.BotInvoiceUrl, purchaseId, nil
}

func (s PaymentService) createYookasaInvoiceWithMessageID(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string, messageID *int64) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeYookasa,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
		MessageID:   messageID,
	})
	if err != nil {
		slog.Error("Error creating purchase", "error", err)
		return "", 0, err
	}

	payment, err := s.yookasaClient.CreateInvoice(ctx, amount, months, customer.ID, purchaseId)
	if err != nil {
		slog.Error("Error creating payment", "error", err)
		return "", 0, err
	}

	updates := map[string]interface{}{
		"yookasa_url": payment.Confirmation.ConfirmationURL,
		"yookasa_id":  payment.ID,
		"status":      database.PurchaseStatusPending,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, updates)
	if err != nil {
		slog.Error("Error updating purchase", "error", err)
		return "", 0, err
	}

	return payment.Confirmation.ConfirmationURL, purchaseId, nil
}

func (s PaymentService) createTelegramInvoiceWithMessageID(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string, messageID *int64) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeTelegram,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "STARS",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
		MessageID:   messageID,
	})
	if err != nil {
		slog.Error("Error creating purchase", "error", err)
		return "", 0, err
	}

	// Рассчитываем период подписки в секундах (месяцы * 30 дней * 24 часа * 60 минут * 60 секунд)
	subscriptionPeriodSeconds := months * 30 * 24 * 60 * 60

	invoiceURL, err := s.telegramBot.CreateInvoiceLink(ctx, &bot.CreateInvoiceLinkParams{
		Title:    s.translation.GetText(customer.Language, "invoice_title"),
		Currency: "XTR",
		Prices: []models.LabeledPrice{
			{
				Label:  fmt.Sprintf("%s (%d мес.)", s.translation.GetText(customer.Language, fmt.Sprintf("month_%d", months)), months),
				Amount: amount,
			},
		},
		Description:        s.translation.GetText(customer.Language, "invoice_description"),
		Payload:            fmt.Sprintf("%d&%s", purchaseId, ctx.Value("username")),
		SubscriptionPeriod: subscriptionPeriodSeconds,
	})

	if err != nil {
		slog.Error("Error creating invoice", "error", err)
		return "", 0, err
	}

	return invoiceURL, purchaseId, nil
}

func (s PaymentService) createTributeInvoiceWithMessageID(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string, messageID *int64) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeTribute,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
		MessageID:   messageID,
	})
	if err != nil {
		slog.Error("Error creating purchase", "error", err)
		return "", 0, err
	}

	return "", purchaseId, nil
}

// CreateAutoRenewalPayment - создает автоматический платеж для продления подписки
func (ps *PaymentService) CreateAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff, paymentMethod string, savedPaymentData map[string]any) (int64, error) {
	switch paymentMethod {
	case "yookasa":
		return ps.createYookasaAutoRenewalPayment(ctx, customer, tariff, savedPaymentData)
	case "cryptopay":
		return ps.createCryptoPayAutoRenewalPayment(ctx, customer, tariff)
	case "telegram":
		return ps.createTelegramAutoRenewalPayment(ctx, customer, tariff)
	case "tribute":
		return ps.createTributeAutoRenewalPayment(ctx, customer, tariff)
	default:
		return 0, fmt.Errorf("unsupported payment method for auto renewal: %s", paymentMethod)
	}
}

// EnableAutoRenewalAfterPayment - включает автопродление после успешной оплаты
func (ps *PaymentService) EnableAutoRenewalAfterPayment(ctx context.Context, customerID int64, paymentMethod string, tariffCode string, paymentMethodData map[string]any) error {
	// Создаем интерфейс для автопродления через dependency injection
	if ps.autoRenewalInterface != nil {
		err := ps.autoRenewalInterface.EnableAutoRenewalAfterPayment(ctx, customerID, paymentMethod, tariffCode, paymentMethodData)
		if err != nil {
			slog.Error("Failed to enable auto renewal after payment", "error", err, "customer_id", customerID)
			return err
		}

		slog.Info("Auto renewal enabled successfully after payment",
			"customer_id", customerID,
			"payment_method", paymentMethod,
			"tariff_code", tariffCode)
		return nil
	}

	// Fallback: логируем, если интерфейс не настроен
	slog.Warn("Auto renewal interface not configured, skipping auto renewal setup",
		"customer_id", customerID,
		"payment_method", paymentMethod,
		"tariff_code", tariffCode)
	return nil
}

// createAutoRenewalPurchase - создает покупку для автопродления без применения промокодов
func (s PaymentService) createAutoRenewalPurchase(ctx context.Context, tariff *database.Tariff, customer *database.Customer, invoiceType database.InvoiceType) (url string, purchaseId int64, err error) {
	// Извлекаем количество месяцев из кода тарифа
	months := tariff.GetMonthsFromCode()

	// Определяем полную стоимость тарифа БЕЗ применения промокодов
	var amount int
	switch invoiceType {
	case database.InvoiceTypeTelegram:
		amount = tariff.PriceStars
	case database.InvoiceTypeYookasa, database.InvoiceTypeCrypto, database.InvoiceTypeTribute:
		amount = tariff.PriceRUB
	default:
		return "", 0, fmt.Errorf("неизвестный тип оплаты: %s", invoiceType)
	}

	// Создаем покупку с полной стоимостью (без промокода)
	switch invoiceType {
	case database.InvoiceTypeCrypto:
		return s.createCryptoInvoice(ctx, amount, months, customer, "", tariff.Code)
	case database.InvoiceTypeYookasa:
		return s.createYookasaInvoice(ctx, amount, months, customer, "", tariff.Code)
	case database.InvoiceTypeTelegram:
		return s.createTelegramInvoice(ctx, amount, months, customer, "", tariff.Code)
	case database.InvoiceTypeTribute:
		return s.createTributeInvoice(ctx, amount, months, customer, "", tariff.Code)
	default:
		return "", 0, fmt.Errorf("неподдерживаемый тип инвойса для автопродления: %s", invoiceType)
	}
}

// createYookasaAutoRenewalPayment - создает автоматический платеж через Yookasa
func (ps *PaymentService) createYookasaAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff, savedPaymentData map[string]any) (int64, error) {
	// Получаем payment_method_id из сохраненных данных
	paymentMethodID, ok := savedPaymentData["payment_method_id"].(string)
	if !ok || paymentMethodID == "" {
		slog.Info("Нет сохраненного способа оплаты YooKassa, создаем инвойс с сохранением",
			"customer_id", customer.ID)
		return ps.createYookasaInvoiceWithSaveMethod(ctx, customer, tariff)
	}

	// Проверяем срок действия сохраненного способа оплаты
	if expiresAtStr, exists := savedPaymentData["expires_at"].(string); exists && expiresAtStr != "" {
		if expiresAt, err := time.Parse(time.RFC3339, expiresAtStr); err == nil {
			if time.Now().After(expiresAt) {
				slog.Warn("Сохраненный способ оплаты YooKassa истек, создаем новый инвойс",
					"customer_id", customer.ID,
					"payment_method_id", paymentMethodID,
					"expires_at", expiresAt)
				return ps.createYookasaInvoiceWithSaveMethod(ctx, customer, tariff)
			}

			// Проверяем, не истекает ли способ оплаты в ближайшие 7 дней
			if time.Now().Add(7 * 24 * time.Hour).After(expiresAt) {
				slog.Warn("Сохраненный способ оплаты YooKassa истекает в ближайшие 7 дней",
					"customer_id", customer.ID,
					"payment_method_id", paymentMethodID,
					"expires_at", expiresAt,
					"days_until_expiry", int(time.Until(expiresAt).Hours()/24))
			}
		} else {
			slog.Warn("Некорректный формат expires_at в сохраненных данных",
				"customer_id", customer.ID,
				"payment_method_id", paymentMethodID,
				"expires_at_str", expiresAtStr,
				"parse_error", err)
		}
	}

	// Дополнительная валидация payment_method_id через YooKassa API
	if err := ps.validatePaymentMethodOwnership(ctx, customer.ID, paymentMethodID); err != nil {
		slog.Warn("Валидация принадлежности payment_method_id не удалась, создаем новый инвойс",
			"customer_id", customer.ID,
			"payment_method_id", paymentMethodID,
			"error", err)
		return ps.createYookasaInvoiceWithSaveMethod(ctx, customer, tariff)
	}

	// Логируем попытку автоматического списания
	cardMask := "неизвестно"
	cardType := "неизвестно"
	if mask, exists := savedPaymentData["card_mask"].(string); exists {
		cardMask = mask
	}
	if cType, exists := savedPaymentData["card_type"].(string); exists {
		cardType = cType
	}

	slog.Info("Попытка автоматического списания YooKassa",
		"customer_id", customer.ID,
		"payment_method_id", paymentMethodID,
		"card_mask", cardMask,
		"card_type", cardType,
		"amount", tariff.PriceRUB,
		"tariff_code", tariff.Code)

	// Используем сохраненный способ оплаты для автоматического списания
	return ps.createAutomaticYookasaPayment(ctx, customer, tariff, paymentMethodID)
}

// createYookasaInvoiceWithSaveMethod - создает инвойс YooKassa с сохранением способа оплаты
func (ps *PaymentService) createYookasaInvoiceWithSaveMethod(ctx context.Context, customer *database.Customer, tariff *database.Tariff) (int64, error) {
	// Создаем покупку
	purchaseID, err := ps.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeYookasa,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(tariff.PriceRUB),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       tariff.GetMonthsFromCode(),
		TariffCode:  &tariff.Code,
	})
	if err != nil {
		slog.Error("Ошибка создания покупки для автопродления YooKassa", "error", err)
		return 0, err
	}

	// Создаем инвойс с сохранением способа оплаты
	invoice, err := ps.yookasaClient.CreateInvoiceWithSavePaymentMethod(ctx, tariff.PriceRUB, tariff.GetMonthsFromCode(), customer.ID, purchaseID)
	if err != nil {
		slog.Error("Ошибка создания инвойса YooKassa с сохранением способа оплаты", "error", err)
		return 0, err
	}

	// Обновляем покупку с данными инвойса
	updates := map[string]interface{}{
		"yookasa_url": invoice.Confirmation.ConfirmationURL,
		"yookasa_id":  invoice.ID,
		"status":      database.PurchaseStatusPending,
	}

	err = ps.purchaseRepository.UpdateFields(ctx, purchaseID, updates)
	if err != nil {
		slog.Error("Ошибка обновления покупки YooKassa", "error", err)
		return 0, err
	}

	slog.Info("Создан инвойс YooKassa с сохранением способа оплаты",
		"purchase_id", purchaseID,
		"invoice_id", invoice.ID.String(),
		"customer_id", customer.ID)

	return purchaseID, nil
}

// createAutomaticYookasaPayment - создает автоматический платеж с сохраненным способом оплаты
func (ps *PaymentService) createAutomaticYookasaPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff, paymentMethodID string) (int64, error) {
	// Создаем покупку
	purchaseID, err := ps.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeYookasa,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(tariff.PriceRUB),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       tariff.GetMonthsFromCode(),
		TariffCode:  &tariff.Code,
	})
	if err != nil {
		slog.Error("Ошибка создания покупки для автоматического платежа YooKassa", "error", err)
		return 0, err
	}

	// Парсим payment_method_id
	paymentMethodUUID, err := uuid.Parse(paymentMethodID)
	if err != nil {
		slog.Error("Некорректный payment_method_id", "error", err, "payment_method_id", paymentMethodID)

		// Отмечаем покупку как отмененную при ошибке валидации
		cancelUpdates := map[string]interface{}{
			"status": database.PurchaseStatusCancel,
		}
		if cancelErr := ps.purchaseRepository.UpdateFields(ctx, purchaseID, cancelUpdates); cancelErr != nil {
			slog.Error("Ошибка отмены покупки при некорректном payment_method_id", "error", cancelErr, "purchase_id", purchaseID)
		}

		// Создаем fallback инвойс вместо возврата ошибки
		slog.Info("Создаем fallback инвойс из-за некорректного payment_method_id",
			"customer_id", customer.ID,
			"payment_method_id", paymentMethodID)
		return ps.createYookasaInvoiceWithSaveMethod(ctx, customer, tariff)
	}

	// Создаем автоматический платеж с сохраненным способом оплаты
	var monthString string
	months := tariff.GetMonthsFromCode()
	switch months {
	case 1:
		monthString = "месяц"
	case 3:
		monthString = "3 месяца"
	case 6:
		monthString = "6 месяцев"
	case 12:
		monthString = "12 месяцев"
	default:
		monthString = fmt.Sprintf("%d месяцев", months)
	}

	description := fmt.Sprintf("Автопродление подписки на %s", monthString)

	// Создаем запрос на автоматический платеж
	request := yookasa.CreatePaymentWithSavedMethodRequest{
		Amount: yookasa.Amount{
			Value:    fmt.Sprintf("%d", tariff.PriceRUB),
			Currency: "RUB",
		},
		PaymentMethodID: paymentMethodUUID,
		Capture:         true,
		Description:     description,
		Metadata: map[string]any{
			"customer_id":  customer.ID,
			"purchase_id":  purchaseID,
			"auto_renewal": true,
		},
	}

	// Создаем платеж с повторными попытками
	idempotencyKey := uuid.New().String()

	slog.Info("Создание автоматического платежа YooKassa",
		"customer_id", customer.ID,
		"purchase_id", purchaseID,
		"payment_method_id", paymentMethodID,
		"amount", request.Amount.Value,
		"idempotency_key", idempotencyKey)

	payment, err := ps.createPaymentWithRetry(ctx, request, idempotencyKey, paymentMethodID)
	if err != nil {
		// Если автоматический платеж не удался, создаем fallback инвойс
		slog.Warn("Автоматический платеж YooKassa не удался, создаем fallback инвойс",
			"error", err,
			"payment_method_id", paymentMethodID,
			"customer_id", customer.ID,
			"purchase_id", purchaseID,
			"idempotency_key", idempotencyKey)

		// Отмечаем неудачную покупку как отмененную
		cancelUpdates := map[string]interface{}{
			"status": database.PurchaseStatusCancel,
		}
		if cancelErr := ps.purchaseRepository.UpdateFields(ctx, purchaseID, cancelUpdates); cancelErr != nil {
			slog.Error("Ошибка отмены неудачной покупки", "error", cancelErr, "purchase_id", purchaseID)
		}

		// Создаем обычный инвойс как fallback
		return ps.createYookasaInvoiceWithSaveMethod(ctx, customer, tariff)
	}

	// Обновляем покупку с данными платежа
	updates := map[string]interface{}{
		"yookasa_id": payment.ID,
		"status":     database.PurchaseStatusPending,
	}

	err = ps.purchaseRepository.UpdateFields(ctx, purchaseID, updates)
	if err != nil {
		slog.Error("Ошибка обновления покупки автоматического платежа", "error", err)
		return 0, err
	}

	slog.Info("Создан автоматический платеж YooKassa",
		"purchase_id", purchaseID,
		"payment_id", payment.ID.String(),
		"payment_method_id", paymentMethodID,
		"customer_id", customer.ID,
		"amount", tariff.PriceRUB,
		"tariff_code", tariff.Code,
		"payment_status", payment.Status,
		"description", description)

	return purchaseID, nil
}

// createPaymentWithRetry - создает платеж YooKassa с повторными попытками
func (ps *PaymentService) createPaymentWithRetry(ctx context.Context, request yookasa.CreatePaymentWithSavedMethodRequest, idempotencyKey, paymentMethodID string) (*yookasa.Payment, error) {
	const maxRetries = 3
	const baseDelay = time.Second * 2

	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		// Создаем контекст с таймаутом для каждой попытки
		attemptCtx, cancel := context.WithTimeout(ctx, 30*time.Second)

		payment, err := ps.yookasaClient.CreatePaymentWithSavedMethod(attemptCtx, request, idempotencyKey)
		cancel()

		if err == nil {
			slog.Info("Автоматический платеж YooKassa создан успешно",
				"attempt", attempt,
				"payment_id", payment.ID.String(),
				"payment_method_id", paymentMethodID)
			return payment, nil
		}

		lastErr = err

		// Логируем ошибку попытки
		slog.Warn("Попытка создания автоматического платежа YooKassa не удалась",
			"attempt", attempt,
			"max_retries", maxRetries,
			"error", err,
			"payment_method_id", paymentMethodID)

		// Если это не последняя попытка, ждем перед повтором
		if attempt < maxRetries {
			delay := time.Duration(attempt) * baseDelay
			slog.Info("Ожидание перед повторной попыткой", "delay_seconds", delay.Seconds())

			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(delay):
				// Продолжаем к следующей попытке
			}
		}
	}

	slog.Error("Все попытки создания автоматического платежа YooKassa исчерпаны",
		"max_retries", maxRetries,
		"final_error", lastErr,
		"payment_method_id", paymentMethodID)

	return nil, fmt.Errorf("не удалось создать автоматический платеж после %d попыток: %w", maxRetries, lastErr)
}

// createCryptoPayAutoRenewalPayment - создает автоматический платеж через CryptoPay
func (ps *PaymentService) createCryptoPayAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff) (int64, error) {
	// Для CryptoPay создаем инвойс без промокодов и отправляем уведомление пользователю
	_, purchaseID, err := ps.createAutoRenewalPurchase(ctx, tariff, customer, database.InvoiceTypeCrypto)
	if err != nil {
		return 0, fmt.Errorf("failed to create crypto pay purchase: %w", err)
	}

	// Отправляем уведомление пользователю о необходимости оплаты
	ps.sendAutoRenewalCryptoPayNotification(ctx, customer, tariff, purchaseID)

	slog.Info("CryptoPay auto renewal invoice created and notification sent",
		"customer_id", customer.ID,
		"purchase_id", purchaseID,
		"tariff_code", tariff.Code)

	return purchaseID, nil
}

// createTelegramAutoRenewalPayment - создает автоматический платеж через Telegram Stars
func (ps *PaymentService) createTelegramAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff) (int64, error) {
	// Для Telegram Stars создаем инвойс без промокодов
	_, purchaseID, err := ps.createAutoRenewalPurchase(ctx, tariff, customer, database.InvoiceTypeTelegram)
	if err != nil {
		return 0, fmt.Errorf("failed to create telegram purchase: %w", err)
	}

	// Отправляем уведомление пользователю о необходимости оплаты
	ps.sendAutoRenewalTelegramNotification(ctx, customer, tariff, purchaseID)

	slog.Info("Telegram Stars auto renewal invoice created and notification sent",
		"customer_id", customer.ID,
		"purchase_id", purchaseID,
		"tariff_code", tariff.Code)

	return purchaseID, nil
}

// createTributeAutoRenewalPayment - создает автоматический платеж через Tribute
func (ps *PaymentService) createTributeAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff) (int64, error) {
	// Tribute работает через подписки, которые автоматически продлеваются
	// Поэтому для Tribute мы не создаем новые инвойсы, а полагаемся на их систему автопродления

	// Отправляем уведомление пользователю о том, что автопродление работает через Tribute
	ps.sendAutoRenewalTributeNotification(ctx, customer, tariff)

	// Возвращаем 0 как purchase_id, так как реальная покупка будет создана через webhook
	slog.Info("Tribute auto renewal notification sent - renewal handled by Tribute system",
		"customer_id", customer.ID,
		"tariff_code", tariff.Code)

	return 0, nil
}

// sendAutoRenewalCryptoPayNotification - отправляет уведомление об автопродлении через CryptoPay
func (ps *PaymentService) sendAutoRenewalCryptoPayNotification(ctx context.Context, customer *database.Customer, tariff *database.Tariff, purchaseID int64) {
	langCode := customer.Language
	if langCode == "" {
		langCode = "ru"
	}

	// Получаем информацию о покупке
	purchase, err := ps.purchaseRepository.FindById(ctx, purchaseID)
	if err != nil {
		slog.Error("Failed to get purchase for crypto notification", "error", err, "purchase_id", purchaseID)
		return
	}

	// Получаем количество месяцев из кода тарифа
	months := tariff.GetMonthsFromCode()

	// Формируем улучшенное сообщение с использованием переводов
	messageText := fmt.Sprintf(
		ps.translation.GetText(langCode, "cryptopay_auto_renewal_notification"),
		tariff.Title,
		tariff.PriceRUB,
		months,
	)

	// Получаем URL инвойса из покупки
	var invoiceURL string
	if purchase.CryptoInvoiceLink != nil {
		invoiceURL = *purchase.CryptoInvoiceLink
	}

	// Создаем клавиатуру с переводами
	paymentButtonText := ps.translation.GetText(langCode, "cryptopay_payment_button")
	cancelButtonText := ps.translation.GetText(langCode, "cryptopay_cancel_auto_renewal_button")

	keyboard := models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text: paymentButtonText,
					URL:  invoiceURL,
				},
			},
			{
				{
					Text:         cancelButtonText,
					CallbackData: "auto_renewal_disable",
				},
			},
		},
	}

	err = ps.retryTelegramAPI(ctx, func() error {
		_, retryErr := ps.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:      customer.TelegramID,
			Text:        messageText,
			ParseMode:   models.ParseModeHTML,
			ReplyMarkup: keyboard,
		})
		return retryErr
	}, "send_auto_renewal_crypto_pay_notification")

	if err != nil {
		slog.Error("Failed to send auto renewal crypto pay notification after retries", "error", err)
	} else {
		slog.Info("Auto renewal crypto pay notification sent successfully",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID,
			"purchase_id", purchaseID)
	}
}

// sendAutoRenewalTelegramNotification - отправляет уведомление об автопродлении через Telegram Stars
func (ps *PaymentService) sendAutoRenewalTelegramNotification(ctx context.Context, customer *database.Customer, tariff *database.Tariff, purchaseID int64) {
	langCode := customer.Language
	if langCode == "" {
		langCode = "ru"
	}

	// Получаем информацию о покупке для создания инвойса
	purchase, err := ps.purchaseRepository.FindById(ctx, purchaseID)
	if err != nil {
		slog.Error("Failed to get purchase for telegram notification", "error", err, "purchase_id", purchaseID)
		return
	}

	messageText := fmt.Sprintf(
		"🔄 <b>Автопродление подписки</b>\n\n"+
			"📦 Тариф: <b>%s</b>\n"+
			"💰 Стоимость: <b>%d⭐</b>\n"+
			"💳 Способ оплаты: <b>Telegram Stars</b>\n"+
			"⏰ Срок действия: <b>%d мес.</b>\n\n"+
			"Для продления подписки нажмите кнопку ниже и оплатите через Telegram Stars:",
		tariff.Title,
		tariff.PriceStars,
		purchase.Month,
	)

	// Создаем инвойс для Telegram Stars
	invoiceURL, err := ps.createTelegramInvoiceForAutoRenewal(ctx, customer, tariff, purchaseID)
	if err != nil {
		slog.Error("Failed to create telegram invoice for auto renewal", "error", err)
		return
	}

	keyboard := models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text: "⭐ Оплатить через Telegram Stars",
					URL:  invoiceURL,
				},
			},
			{
				{
					Text:         "❌ Отменить автопродление",
					CallbackData: "auto_renewal_disable",
				},
			},
		},
	}

	err = ps.retryTelegramAPI(ctx, func() error {
		_, retryErr := ps.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:      customer.TelegramID,
			Text:        messageText,
			ParseMode:   models.ParseModeHTML,
			ReplyMarkup: keyboard,
		})
		return retryErr
	}, "send_auto_renewal_telegram_notification")

	if err != nil {
		slog.Error("Failed to send auto renewal telegram notification after retries", "error", err)
	} else {
		slog.Info("Auto renewal telegram notification sent successfully",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID,
			"purchase_id", purchaseID)
	}
}

// getPaymentMethodFromPurchase - определяет способ оплаты по данным покупки
func (ps *PaymentService) getPaymentMethodFromPurchase(purchase *database.Purchase) string {
	// Используем InvoiceType для определения способа оплаты
	switch purchase.InvoiceType {
	case database.InvoiceTypeYookasa:
		return "yookasa"
	case database.InvoiceTypeCrypto:
		return "cryptopay"
	case database.InvoiceTypeTelegram:
		return "telegram"
	case database.InvoiceTypeTribute:
		return "tribute"
	default:
		return "unknown"
	}
}

// extractPaymentMethodData - извлекает данные способа оплаты для сохранения
func (ps *PaymentService) extractPaymentMethodData(purchase *database.Purchase, paymentMethod string) map[string]any {
	data := make(map[string]any)

	switch paymentMethod {
	case "yookasa":
		if purchase.YookasaID != nil {
			data["yookasa_id"] = purchase.YookasaID.String()

			slog.Info("Начинаем извлечение данных способа оплаты YooKassa",
				"payment_id", purchase.YookasaID.String(),
				"customer_id", purchase.CustomerID)

			// Получаем payment_method_id из Yookasa API если платеж был с сохранением
			ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
			defer cancel()

			var payment *yookasa.Payment
			err := ps.retryYooKassaAPI(ctx, func() error {
				var retryErr error
				payment, retryErr = ps.yookasaClient.GetPayment(ctx, *purchase.YookasaID)
				return retryErr
			}, "get_payment")

			if err != nil {
				slog.Error("Ошибка получения данных платежа YooKassa после retry",
					"error", err,
					"payment_id", purchase.YookasaID.String(),
					"customer_id", purchase.CustomerID)
				return data
			}

			if payment == nil {
				slog.Warn("Получен пустой ответ от YooKassa API",
					"payment_id", purchase.YookasaID.String())
				return data
			}

			slog.Info("Получены данные платежа YooKassa",
				"payment_id", payment.ID.String(),
				"status", payment.Status,
				"paid", payment.Paid,
				"payment_method_type", payment.PaymentMethod.Type,
				"payment_method_saved", payment.PaymentMethod.Saved,
				"payment_method_id_empty", payment.PaymentMethod.ID == uuid.Nil)

			// Проверяем, был ли способ оплаты сохранен
			if payment.PaymentMethod.Saved && payment.PaymentMethod.ID != uuid.Nil {
				// Сохраняем базовые данные способа оплаты
				data["payment_method_id"] = payment.PaymentMethod.ID.String()
				data["payment_type"] = payment.PaymentMethod.Type

				slog.Info("Найден сохраненный способ оплаты YooKassa",
					"payment_method_id", payment.PaymentMethod.ID.String(),
					"payment_type", payment.PaymentMethod.Type)

				// Получаем детальную информацию о сохраненном способе оплаты
				customerIDStr := fmt.Sprintf("%d", purchase.CustomerID)

				var savedMethods *yookasa.PaymentMethodListResponse
				err := ps.retryYooKassaAPI(ctx, func() error {
					var retryErr error
					savedMethods, retryErr = ps.yookasaClient.GetSavedPaymentMethods(ctx, customerIDStr)
					return retryErr
				}, "get_saved_payment_methods")

				if err != nil {
					slog.Error("Ошибка получения сохраненных способов оплаты после retry",
						"error", err,
						"customer_id", customerIDStr,
						"payment_method_id", payment.PaymentMethod.ID.String())
					// Не возвращаем ошибку, базовые данные уже сохранены
				} else if savedMethods != nil {
					slog.Info("Получен список сохраненных способов оплаты",
						"customer_id", customerIDStr,
						"methods_count", len(savedMethods.Items))

					// Ищем наш способ оплаты в списке сохраненных
					for _, method := range savedMethods.Items {
						if method.ID == payment.PaymentMethod.ID {
							// Сохраняем информацию о карте (если доступна)
							if method.Card != nil {
								data["card_mask"] = fmt.Sprintf("%s****%s", method.Card.First6, method.Card.Last4)
								data["card_type"] = method.Card.CardType
								data["expiry_year"] = method.Card.ExpiryYear
								data["expiry_month"] = method.Card.ExpiryMonth
							}

							// Сохраняем срок действия способа оплаты
							if !method.ExpiresAt.IsZero() {
								data["expires_at"] = method.ExpiresAt.Format(time.RFC3339)
							}

							slog.Info("Сохранены детальные данные способа оплаты YooKassa",
								"payment_method_id", method.ID.String(),
								"card_mask", data["card_mask"],
								"card_type", data["card_type"],
								"expires_at", data["expires_at"])
							break
						}
					}
				}
			} else {
				slog.Warn("Способ оплаты YooKassa не был сохранен или ID пустой",
					"payment_id", payment.ID.String(),
					"saved", payment.PaymentMethod.Saved,
					"id_empty", payment.PaymentMethod.ID == uuid.Nil,
					"payment_method_type", payment.PaymentMethod.Type)
			}
		} else {
			slog.Warn("YookasaID отсутствует в покупке", "purchase_id", purchase.ID)
		}
	case "cryptopay":
		if purchase.CryptoInvoiceID != nil {
			data["crypto_invoice_id"] = *purchase.CryptoInvoiceID
		}
	case "telegram":
		// Для Telegram Stars сохраняем данные подписки
		data["payment_type"] = "telegram_stars"

		// Сохраняем базовую информацию о покупке
		data["purchase_id"] = purchase.ID
		data["amount"] = purchase.Amount
		data["currency"] = purchase.Currency

		// Данные подписки будут добавлены после получения SuccessfulPayment
		// с полями subscription_period, is_recurring, subscription_expiration_date
		data["auto_renewal_enabled"] = true // По умолчанию включаем автопродление для Telegram Stars
	case "tribute":
		// Для Tribute пока что сохраняем только тип
		data["payment_type"] = "tribute"
	}

	return data
}

// UpdateTelegramStarsSubscriptionData - обновляет данные подписки Telegram Stars после получения SuccessfulPayment
// Этот метод вызывается из обработчика платежей для сохранения данных подписки в автопродлении
func (ps *PaymentService) UpdateTelegramStarsSubscriptionData(ctx context.Context, customerID int64, subscriptionData map[string]any) error {
	// Получаем настройки автопродления для пользователя
	autoRenewalSettings, err := ps.autoRenewalRepository.GetByCustomerID(ctx, customerID)
	if err != nil {
		slog.Error("Error getting auto renewal settings for subscription data update", "error", err, "customer_id", customerID)
		return err
	}

	if autoRenewalSettings == nil {
		slog.Warn("No auto renewal settings found for customer", "customer_id", customerID)
		return nil
	}

	// Обновляем сохраненные данные платежного метода с информацией о подписке
	if autoRenewalSettings.SavedPaymentData == nil {
		autoRenewalSettings.SavedPaymentData = make(map[string]any)
	}

	// Добавляем данные подписки к существующим данным
	for key, value := range subscriptionData {
		autoRenewalSettings.SavedPaymentData[key] = value
	}

	// Сохраняем обновленные данные
	err = ps.autoRenewalRepository.UpdateSavedPaymentData(ctx, customerID, autoRenewalSettings.SavedPaymentData)
	if err != nil {
		slog.Error("Error updating saved payment data with subscription info", "error", err, "customer_id", customerID)
		return err
	}

	slog.Info("Successfully updated Telegram Stars subscription data",
		"customer_id", customerID,
		"subscription_data", subscriptionData)

	return nil
}

// createTelegramInvoiceForAutoRenewal - создает инвойс Telegram Stars для автопродления
func (ps *PaymentService) createTelegramInvoiceForAutoRenewal(ctx context.Context, customer *database.Customer, tariff *database.Tariff, purchaseID int64) (string, error) {
	// Получаем информацию о покупке
	purchase, err := ps.purchaseRepository.FindById(ctx, purchaseID)
	if err != nil {
		return "", fmt.Errorf("failed to get purchase: %w", err)
	}

	// Рассчитываем период подписки в секундах (месяцы * 30 дней * 24 часа * 60 минут * 60 секунд)
	subscriptionPeriodSeconds := purchase.Month * 30 * 24 * 60 * 60

	// Создаем инвойс через Telegram Bot API с поддержкой подписок
	invoiceURL, err := ps.telegramBot.CreateInvoiceLink(ctx, &bot.CreateInvoiceLinkParams{
		Title:    ps.translation.GetText(customer.Language, "auto_renewal_invoice_title"),
		Currency: "XTR",
		Prices: []models.LabeledPrice{
			{
				Label:  fmt.Sprintf("%s (%d мес.)", tariff.Title, purchase.Month),
				Amount: tariff.PriceStars,
			},
		},
		Description: fmt.Sprintf(
			ps.translation.GetText(customer.Language, "auto_renewal_invoice_description"),
			tariff.Title,
		),
		Payload:            fmt.Sprintf("%d&%s", purchaseID, "auto_renewal"),
		SubscriptionPeriod: subscriptionPeriodSeconds,
	})

	if err != nil {
		return "", fmt.Errorf("failed to create telegram invoice: %w", err)
	}

	return invoiceURL, nil
}

// sendAutoRenewalTributeNotification - отправляет уведомление об автопродлении через Tribute
func (ps *PaymentService) sendAutoRenewalTributeNotification(ctx context.Context, customer *database.Customer, tariff *database.Tariff) {
	langCode := customer.Language
	if langCode == "" {
		langCode = "ru"
	}

	messageText := fmt.Sprintf(
		"🔄 <b>Автопродление подписки</b>\n\n"+
			"📦 Тариф: <b>%s</b>\n"+
			"💰 Стоимость: <b>%d₽</b>\n"+
			"💳 Способ оплаты: <b>Tribute</b>\n\n"+
			"✅ <b>Автопродление активно!</b>\n\n"+
			"Ваша подписка будет автоматически продлена через систему Tribute. "+
			"Никаких дополнительных действий не требуется.\n\n"+
			"Если вы хотите отменить автопродление, сделайте это в настройках Tribute.",
		tariff.Title,
		tariff.PriceRUB,
	)

	keyboard := models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text: "🏛️ Управление подпиской",
					URL:  config.GetTributePaymentUrl(),
				},
			},
			{
				{
					Text:         "❌ Отключить автопродление",
					CallbackData: "auto_renewal_disable",
				},
			},
		},
	}

	err := ps.retryTelegramAPI(ctx, func() error {
		_, retryErr := ps.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:      customer.TelegramID,
			Text:        messageText,
			ParseMode:   models.ParseModeHTML,
			ReplyMarkup: keyboard,
		})
		return retryErr
	}, "send_auto_renewal_tribute_notification")

	if err != nil {
		slog.Error("Failed to send auto renewal tribute notification after retries", "error", err)
	} else {
		slog.Info("Auto renewal tribute notification sent successfully",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID)
	}
}

// retryWithBackoff - выполняет функцию с экспоненциальным backoff при ошибках
func retryWithBackoff(ctx context.Context, operation func() error, maxRetries int, baseDelay time.Duration, operationName string) error {
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			// Экспоненциальный backoff с jitter
			delay := time.Duration(float64(baseDelay) * math.Pow(2, float64(attempt-1)))
			if delay > 30*time.Second {
				delay = 30 * time.Second // Максимальная задержка 30 секунд
			}

			slog.Debug("Retrying operation after delay",
				"operation", operationName,
				"attempt", attempt+1,
				"max_retries", maxRetries,
				"delay_seconds", delay.Seconds(),
				"last_error", lastErr)

			select {
			case <-ctx.Done():
				return fmt.Errorf("context cancelled during retry for %s: %w", operationName, ctx.Err())
			case <-time.After(delay):
			}
		}

		lastErr = operation()
		if lastErr == nil {
			if attempt > 0 {
				slog.Info("Operation succeeded after retry",
					"operation", operationName,
					"attempt", attempt+1)
			}
			return nil
		}

		slog.Debug("Operation failed",
			"operation", operationName,
			"attempt", attempt+1,
			"error", lastErr)
	}

	return fmt.Errorf("operation %s failed after %d attempts: %w", operationName, maxRetries, lastErr)
}

// retryTelegramAPI - специализированная функция retry для Telegram API
func (ps *PaymentService) retryTelegramAPI(ctx context.Context, operation func() error, operationName string) error {
	return retryWithBackoff(ctx, operation, 3, 1*time.Second, fmt.Sprintf("telegram_api_%s", operationName))
}

// retryYooKassaAPI - специализированная функция retry для YooKassa API
func (ps *PaymentService) retryYooKassaAPI(ctx context.Context, operation func() error, operationName string) error {
	return retryWithBackoff(ctx, operation, 5, 2*time.Second, fmt.Sprintf("yookassa_api_%s", operationName))
}

// validatePaymentMethodOwnership - проверяет принадлежность payment_method_id к customer
func (ps *PaymentService) validatePaymentMethodOwnership(ctx context.Context, customerID int64, paymentMethodID string) error {
	customerIDStr := fmt.Sprintf("%d", customerID)

	var savedMethods *yookasa.PaymentMethodListResponse
	err := ps.retryYooKassaAPI(ctx, func() error {
		var retryErr error
		savedMethods, retryErr = ps.yookasaClient.GetSavedPaymentMethods(ctx, customerIDStr)
		return retryErr
	}, "validate_payment_method_ownership")

	if err != nil {
		return fmt.Errorf("failed to get saved payment methods for validation: %w", err)
	}

	if savedMethods == nil || len(savedMethods.Items) == 0 {
		return fmt.Errorf("no saved payment methods found for customer %d", customerID)
	}

	// Проверяем, есть ли payment_method_id среди сохраненных способов оплаты
	for _, method := range savedMethods.Items {
		if method.ID.String() == paymentMethodID {
			slog.Debug("Payment method ownership validated successfully",
				"customer_id", customerID,
				"payment_method_id", paymentMethodID,
				"card_type", method.Card.CardType,
				"card_mask", method.Card.Last4)
			return nil
		}
	}

	return fmt.Errorf("payment_method_id %s does not belong to customer %d", paymentMethodID, customerID)
}
