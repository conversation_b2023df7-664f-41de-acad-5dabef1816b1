package handler

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"remnawave-tg-shop-bot/internal/database"
)

// MockCustomerRepository - мок для репозитория клиентов
type MockCustomerRepository struct {
	mock.Mock
}

func (m *MockCustomerRepository) FindByTelegramId(ctx context.Context, telegramID int64) (*database.Customer, error) {
	args := m.Called(ctx, telegramID)
	return args.Get(0).(*database.Customer), args.Error(1)
}

// MockAutoRenewalService - мок для сервиса автопродления
type MockAutoRenewalService struct {
	mock.Mock
}

func (m *MockAutoRenewalService) GetAutoRenewalSettings(ctx context.Context, customerID int64) (*database.AutoRenewalSettings, error) {
	args := m.Called(ctx, customerID)
	return args.Get(0).(*database.AutoRenewalSettings), args.Error(1)
}

func (m *MockAutoRenewalService) ToggleAutoRenewal(ctx context.Context, customerID int64) error {
	args := m.Called(ctx, customerID)
	return args.Error(0)
}

func (m *MockAutoRenewalService) SetupAutoRenewal(ctx context.Context, customerID int64, invoiceType database.InvoiceType) error {
	args := m.Called(ctx, customerID, invoiceType)
	return args.Error(0)
}

// MockTelegramBot - мок для Telegram бота
type MockTelegramBot struct {
	mock.Mock
}

func (m *MockTelegramBot) EditMessageText(ctx context.Context, params interface{}) error {
	args := m.Called(ctx, params)
	return args.Error(0)
}

// Тестовые данные
func createTestCustomerForHandler() *database.Customer {
	return &database.Customer{
		ID:         123,
		TelegramID: 456789,
		Language:   "ru",
	}
}

func createTestAutoRenewalSettings(enabled bool, invoiceType database.InvoiceType) *database.AutoRenewalSettings {
	savedPaymentData := map[string]any{}
	if enabled && invoiceType == database.InvoiceTypeYookasa {
		savedPaymentData = map[string]any{
			"payment_method_id": "pm_test_123456",
			"card_mask":         "**** 1234",
			"card_type":         "MasterCard",
		}
	}

	savedPaymentDataJSON, _ := json.Marshal(savedPaymentData)

	return &database.AutoRenewalSettings{
		ID:               1,
		CustomerID:       123,
		Enabled:          enabled,
		InvoiceType:      invoiceType,
		SavedPaymentData: savedPaymentDataJSON,
	}
}

// TestHasSavedPaymentMethod - тест проверки наличия сохраненного способа оплаты
func TestHasSavedPaymentMethod(t *testing.T) {
	tests := []struct {
		name           string
		settings       *database.AutoRenewalSettings
		expectedResult bool
		expectedError  bool
	}{
		{
			name:           "YooKassa с сохраненным способом оплаты",
			settings:       createTestAutoRenewalSettings(true, database.InvoiceTypeYookasa),
			expectedResult: true,
			expectedError:  false,
		},
		{
			name: "YooKassa без сохраненного способа оплаты",
			settings: &database.AutoRenewalSettings{
				ID:               1,
				CustomerID:       123,
				Enabled:          true,
				InvoiceType:      database.InvoiceTypeYookasa,
				SavedPaymentData: []byte(`{}`),
			},
			expectedResult: false,
			expectedError:  false,
		},
		{
			name: "YooKassa с некорректным payment_method_id",
			settings: &database.AutoRenewalSettings{
				ID:               1,
				CustomerID:       123,
				Enabled:          true,
				InvoiceType:      database.InvoiceTypeYookasa,
				SavedPaymentData: []byte(`{"payment_method_id": "invalid-uuid"}`),
			},
			expectedResult: false,
			expectedError:  false,
		},
		{
			name:           "Telegram Stars всегда имеет способ оплаты",
			settings:       createTestAutoRenewalSettings(true, database.InvoiceTypeTelegram),
			expectedResult: true,
			expectedError:  false,
		},
		{
			name:           "CryptoPay всегда имеет способ оплаты",
			settings:       createTestAutoRenewalSettings(true, database.InvoiceTypeCrypto),
			expectedResult: true,
			expectedError:  false,
		},
		{
			name:           "Tribute всегда имеет способ оплаты",
			settings:       createTestAutoRenewalSettings(true, database.InvoiceTypeTribute),
			expectedResult: true,
			expectedError:  false,
		},
		{
			name: "Некорректный JSON в SavedPaymentData",
			settings: &database.AutoRenewalSettings{
				ID:               1,
				CustomerID:       123,
				Enabled:          true,
				InvoiceType:      database.InvoiceTypeYookasa,
				SavedPaymentData: []byte(`invalid json`),
			},
			expectedResult: false,
			expectedError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Создаем handler
			handler := &AutoRenewalHandler{}

			// Выполняем тест
			result, err := handler.hasSavedPaymentMethod(tt.settings)

			// Проверяем результат
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}
		})
	}
}

// TestGetCustomerAndSettings - тест получения клиента и настроек автопродления
func TestGetCustomerAndSettings(t *testing.T) {
	tests := []struct {
		name          string
		chatID        int64
		setupMocks    func(*MockCustomerRepository, *MockAutoRenewalService)
		expectedError bool
	}{
		{
			name:   "Успешное получение клиента и настроек",
			chatID: 456789,
			setupMocks: func(customerRepo *MockCustomerRepository, autoRenewalService *MockAutoRenewalService) {
				customer := createTestCustomerForHandler()
				customerRepo.On("FindByTelegramId", mock.Anything, int64(456789)).Return(customer, nil)

				settings := createTestAutoRenewalSettings(true, database.InvoiceTypeYookasa)
				autoRenewalService.On("GetAutoRenewalSettings", mock.Anything, int64(123)).Return(settings, nil)
			},
			expectedError: false,
		},
		{
			name:   "Клиент не найден",
			chatID: 999999,
			setupMocks: func(customerRepo *MockCustomerRepository, autoRenewalService *MockAutoRenewalService) {
				customerRepo.On("FindByTelegramId", mock.Anything, int64(999999)).Return((*database.Customer)(nil), nil)
			},
			expectedError: true,
		},
		{
			name:   "Настройки автопродления не найдены",
			chatID: 456789,
			setupMocks: func(customerRepo *MockCustomerRepository, autoRenewalService *MockAutoRenewalService) {
				customer := createTestCustomerForHandler()
				customerRepo.On("FindByTelegramId", mock.Anything, int64(456789)).Return(customer, nil)

				autoRenewalService.On("GetAutoRenewalSettings", mock.Anything, int64(123)).Return((*database.AutoRenewalSettings)(nil), assert.AnError)
			},
			expectedError: false, // Функция должна возвращать nil для settings, но не ошибку
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Создаем моки
			mockCustomerRepo := new(MockCustomerRepository)
			mockAutoRenewalService := new(MockAutoRenewalService)

			// Настраиваем моки
			tt.setupMocks(mockCustomerRepo, mockAutoRenewalService)

			// Создаем handler
			handler := &AutoRenewalHandler{
				customerRepository: mockCustomerRepo,
				autoRenewalService: mockAutoRenewalService,
			}

			// Выполняем тест
			customer, settings, err := handler.getCustomerAndSettings(context.Background(), tt.chatID)

			// Проверяем результат
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if customer != nil {
					assert.Equal(t, tt.chatID, customer.TelegramID)
				}
			}

			// Проверяем, что все ожидаемые вызовы были сделаны
			mockCustomerRepo.AssertExpectations(t)
			mockAutoRenewalService.AssertExpectations(t)
		})
	}
}

// TestBuildAutoRenewalInterface - тест построения интерфейса автопродления
func TestBuildAutoRenewalInterface(t *testing.T) {
	tests := []struct {
		name                string
		customer            *database.Customer
		settings            *database.AutoRenewalSettings
		expectedButtonCount int
	}{
		{
			name:                "Автопродление включено с сохраненным способом оплаты",
			customer:            createTestCustomerForHandler(),
			settings:            createTestAutoRenewalSettings(true, database.InvoiceTypeYookasa),
			expectedButtonCount: 2, // Отключить + Настроить
		},
		{
			name:                "Автопродление отключено",
			customer:            createTestCustomerForHandler(),
			settings:            createTestAutoRenewalSettings(false, database.InvoiceTypeYookasa),
			expectedButtonCount: 4, // 4 способа оплаты для включения
		},
		{
			name:                "Настройки автопродления отсутствуют",
			customer:            createTestCustomerForHandler(),
			settings:            nil,
			expectedButtonCount: 4, // 4 способа оплаты для включения
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Создаем handler с мок-переводчиком
			handler := &AutoRenewalHandler{}

			// Выполняем тест
			text, keyboard := handler.buildAutoRenewalInterface(tt.customer, tt.settings)

			// Проверяем результат
			assert.NotEmpty(t, text)
			assert.NotNil(t, keyboard)

			// Подсчитываем количество кнопок
			buttonCount := 0
			for _, row := range keyboard.InlineKeyboard {
				buttonCount += len(row)
			}
			assert.Equal(t, tt.expectedButtonCount, buttonCount)
		})
	}
}
