package handler

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/payment"
	"remnawave-tg-shop-bot/internal/service"
	"remnawave-tg-shop-bot/internal/translation"
	"strconv"
	"strings"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
	"github.com/google/uuid"
)

// AutoRenewalHandler - обработчик для управления автопродлением
type AutoRenewalHandler struct {
	autoRenewalService *service.AutoRenewalService
	customerRepository *database.CustomerRepository
	tariffRepository   *database.TariffRepository
	translation        *translation.Manager
	paymentService     *payment.PaymentService
}

// NewAutoRenewalHandler - создает новый экземпляр обработчика автопродления
func NewAutoRenewalHandler(
	autoRenewalService *service.AutoRenewalService,
	customerRepository *database.CustomerRepository,
	tariffRepository *database.TariffRepository,
	translation *translation.Manager,
	paymentService *payment.PaymentService,
) *AutoRenewalHandler {
	return &AutoRenewalHandler{
		autoRenewalService: autoRenewalService,
		customerRepository: customerRepository,
		tariffRepository:   tariffRepository,
		translation:        translation,
		paymentService:     paymentService,
	}
}

// HandleAutoRenewalToggle - обработчик включения/выключения автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalToggle(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	slog.Info("HandleAutoRenewalToggle called",
		"chat_id", callback.Chat.ID,
		"user_id", update.CallbackQuery.From.ID,
		"callback_data", update.CallbackQuery.Data)

	// Используем общую логику для получения customer и settings
	customer, settings, err := h.getCustomerAndSettings(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error getting customer and settings", "error", err)
		return
	}

	// Определяем состояние автопродления и показываем соответствующий интерфейс
	messageText, keyboard := h.buildAutoRenewalInterface(ctx, customer, settings, langCode, "toggle")

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:      callback.Chat.ID,
		MessageID:   callback.ID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Error editing message in HandleAutoRenewalToggle",
			"error", err,
			"chat_id", callback.Chat.ID,
			"message_id", callback.ID)
	}
}

// HandleAutoRenewalDisable - обработчик отключения автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalDisable(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Отключаем автопродление
	err = h.autoRenewalService.DisableAutoRenewal(ctx, customer.ID)
	if err != nil {
		slog.Error("Error disabling auto renewal", "error", err)

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      h.translation.GetText(langCode, "auto_renewal_disable_error"),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackConnect}},
				},
			},
		})
		return
	}

	// Показываем сообщение об успешном отключении
	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      h.translation.GetText(langCode, "auto_renewal_disabled_success"),
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{{Text: h.translation.GetText(langCode, "back_to_subscription_button"), CallbackData: CallbackConnect}},
			},
		},
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// HandleAutoRenewalChangePayment - обработчик изменения способа оплаты для автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalChangePayment(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Получаем текущие настройки автопродления для получения tariffCode
	autoRenewalSettings, err := h.autoRenewalService.GetAutoRenewalSettings(ctx, customer.ID)
	if err != nil || autoRenewalSettings == nil {
		slog.Error("Auto renewal settings not found", "customer_id", customer.ID, "error", err)
		return
	}

	// Показываем доступные способы оплаты
	messageText := h.translation.GetText(langCode, "auto_renewal_change_payment_message")
	keyboard := h.createPaymentMethodSelectionKeyboard(langCode, autoRenewalSettings.TariffCode)

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:      callback.Chat.ID,
		MessageID:   callback.ID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// HandleAutoRenewalDeleteData - обработчик удаления сохраненных платежных данных
func (h *AutoRenewalHandler) HandleAutoRenewalDeleteData(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Удаляем настройки автопродления и все сохраненные данные
	err = h.autoRenewalService.DeleteAutoRenewalSettings(ctx, customer.ID)
	if err != nil {
		slog.Error("Error deleting auto renewal settings", "error", err)

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      h.translation.GetText(langCode, "auto_renewal_delete_data_error"),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackConnect}},
				},
			},
		})
		return
	}

	// Показываем сообщение об успешном удалении
	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      h.translation.GetText(langCode, "auto_renewal_data_deleted_success"),
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{{Text: h.translation.GetText(langCode, "back_to_subscription_button"), CallbackData: CallbackConnect}},
			},
		},
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// HandleAutoRenewalSetPaymentMethod - обрабатывает изменение способа оплаты для существующего автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalSetPaymentMethod(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	// Извлекаем данные из callback data: auto_renewal_set_payment_{method}_{tariffCode}
	callbackData := update.CallbackQuery.Data
	parts := strings.Split(callbackData, "_")
	if len(parts) < 6 {
		slog.Error("Invalid callback data format", "data", callbackData)
		return
	}

	paymentMethod := parts[4] // auto_renewal_set_payment_yookasa_1m -> yookasa
	tariffCode := parts[5]    // auto_renewal_set_payment_yookasa_1m -> 1m

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Получаем текущие настройки автопродления
	autoRenewalSettings, err := h.autoRenewalService.GetAutoRenewalSettings(ctx, customer.ID)
	if err != nil || autoRenewalSettings == nil {
		slog.Error("Auto renewal settings not found", "customer_id", customer.ID, "error", err)

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      h.translation.GetText(langCode, "auto_renewal_not_enabled"),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackConnect}},
				},
			},
		})
		return
	}

	// Проверяем, что tariffCode соответствует текущим настройкам
	if autoRenewalSettings.TariffCode != tariffCode {
		slog.Error("Tariff code mismatch", "expected", autoRenewalSettings.TariffCode, "received", tariffCode)

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      h.translation.GetText(langCode, "auto_renewal_update_payment_error"),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackConnect}},
				},
			},
		})
		return
	}

	// Обновляем способ оплаты в настройках автопродления
	err = h.autoRenewalService.UpdatePaymentMethod(ctx, customer.ID, paymentMethod)
	if err != nil {
		slog.Error("Error updating payment method", "error", err)

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      h.translation.GetText(langCode, "auto_renewal_update_payment_error"),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackConnect}},
				},
			},
		})
		return
	}

	// Показываем сообщение об успешном обновлении
	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      h.translation.GetText(langCode, "auto_renewal_payment_method_updated"),
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{{Text: h.translation.GetText(langCode, "back_to_subscription_button"), CallbackData: CallbackConnect}},
			},
		},
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// createAutoRenewalSetupKeyboard - создает клавиатуру для настройки автопродления
func (h *AutoRenewalHandler) createAutoRenewalSetupKeyboard(langCode string) models.InlineKeyboardMarkup {
	return models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text:         h.translation.GetText(langCode, "setup_auto_renewal_button"),
					CallbackData: "auto_renewal_setup_start",
				},
			},
			{
				{
					Text:         h.translation.GetText(langCode, "back_button"),
					CallbackData: CallbackConnect,
				},
			},
		},
	}
}

// createAutoRenewalManageKeyboard - создает клавиатуру для управления автопродлением
func (h *AutoRenewalHandler) createAutoRenewalManageKeyboard(langCode string) models.InlineKeyboardMarkup {
	return models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text:         h.translation.GetText(langCode, "change_payment_method_button"),
					CallbackData: "auto_renewal_change_payment",
				},
			},
			{
				{
					Text:         h.translation.GetText(langCode, "disable_auto_renewal_button"),
					CallbackData: "auto_renewal_disable",
				},
			},
			{
				{
					Text:         h.translation.GetText(langCode, "delete_payment_data_button"),
					CallbackData: "auto_renewal_delete_data",
				},
			},
			{
				{
					Text:         h.translation.GetText(langCode, "back_button"),
					CallbackData: CallbackConnect,
				},
			},
		},
	}
}

// createPaymentMethodSelectionKeyboard - создает клавиатуру для выбора способа оплаты
func (h *AutoRenewalHandler) createPaymentMethodSelectionKeyboard(langCode, tariffCode string) models.InlineKeyboardMarkup {
	var keyboard [][]models.InlineKeyboardButton

	// Добавляем доступные способы оплаты с tariffCode
	// TODO: Проверить доступность каждого способа оплаты через конфигурацию
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "payment_method_card"),
			CallbackData: fmt.Sprintf("auto_renewal_set_payment_yookasa_%s", tariffCode),
		},
	})

	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "payment_method_crypto"),
			CallbackData: fmt.Sprintf("auto_renewal_set_payment_cryptopay_%s", tariffCode),
		},
	})

	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "payment_method_stars"),
			CallbackData: fmt.Sprintf("auto_renewal_set_payment_telegram_%s", tariffCode),
		},
	})

	// Кнопка назад
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "back_button"),
			CallbackData: "auto_renewal_toggle",
		},
	})

	return models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}

// buildAutoRenewalStatusText - формирует текст с информацией о статусе автопродления
func (h *AutoRenewalHandler) buildAutoRenewalStatusText(settings *database.AutoRenewalSettings, tariff *database.Tariff, langCode string) string {
	var info strings.Builder

	info.WriteString(h.translation.GetText(langCode, "auto_renewal_enabled_message"))
	info.WriteString("\n\n")

	if tariff != nil {
		info.WriteString(fmt.Sprintf(
			h.translation.GetText(langCode, "auto_renewal_tariff_info"),
			tariff.Title,
		))
		info.WriteString("\n")

		// Определяем стоимость в зависимости от способа оплаты
		var price int
		var currency string
		switch settings.PaymentMethod {
		case "yookasa", "cryptopay", "tribute":
			price = tariff.PriceRUB
			currency = "₽"
		case "telegram":
			price = tariff.PriceStars
			currency = "⭐"
		default:
			price = tariff.PriceRUB
			currency = "₽"
		}

		info.WriteString(fmt.Sprintf(
			h.translation.GetText(langCode, "auto_renewal_price_info"),
			price,
			currency,
		))
		info.WriteString("\n")
	}

	info.WriteString(fmt.Sprintf(
		h.translation.GetText(langCode, "auto_renewal_payment_method_info"),
		h.getPaymentMethodName(settings.PaymentMethod, langCode),
	))

	return info.String()
}

// getPaymentMethodName - возвращает локализованное название способа оплаты
func (h *AutoRenewalHandler) getPaymentMethodName(paymentMethod, langCode string) string {
	switch paymentMethod {
	case "yookasa":
		return h.translation.GetText(langCode, "payment_method_card")
	case "cryptopay":
		return h.translation.GetText(langCode, "payment_method_crypto")
	case "telegram":
		return h.translation.GetText(langCode, "payment_method_stars")
	case "tribute":
		return h.translation.GetText(langCode, "payment_method_tribute")
	default:
		return paymentMethod
	}
}

// HandleAutoRenewalSetupStart - начинает процесс настройки автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalSetupStart(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	slog.Info("HandleAutoRenewalSetupStart called",
		"chat_id", callback.Chat.ID,
		"user_id", update.CallbackQuery.From.ID,
		"callback_data", update.CallbackQuery.Data)

	// Используем общую логику для получения customer и settings
	customer, settings, err := h.getCustomerAndSettings(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error getting customer and settings", "error", err)
		return
	}

	// Определяем состояние автопродления и показываем соответствующий интерфейс
	messageText, keyboard := h.buildAutoRenewalInterface(ctx, customer, settings, langCode, "setup")

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:      callback.Chat.ID,
		MessageID:   callback.ID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Error editing message in HandleAutoRenewalSetupStart",
			"error", err,
			"chat_id", callback.Chat.ID,
			"message_id", callback.ID)
	}
}

// HandleAutoRenewalSelectTariff - обрабатывает выбор тарифа для автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalSelectTariff(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	// Извлекаем код тарифа из callback data
	callbackData := update.CallbackQuery.Data
	tariffCode := callbackData[len("auto_renewal_select_tariff_"):] // Убираем префикс

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Проверяем, что тариф существует
	tariff, err := h.tariffRepository.GetByCode(ctx, tariffCode)
	if err != nil || tariff == nil {
		slog.Error("Tariff not found", "tariff_code", tariffCode, "error", err)
		return
	}

	// Показываем выбор способов оплаты
	messageText := fmt.Sprintf(
		h.translation.GetText(langCode, "auto_renewal_select_payment_message"),
		tariff.Title,
	)
	keyboard := h.createPaymentMethodSelectionKeyboardForSetup(langCode, tariffCode)

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:      callback.Chat.ID,
		MessageID:   callback.ID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// HandleAutoRenewalSelectPaymentMethod - обрабатывает выбор способа оплаты для автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalSelectPaymentMethod(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	// Извлекаем данные из callback data: auto_renewal_setup_payment_{method}_{tariffCode}
	callbackData := update.CallbackQuery.Data
	parts := strings.Split(callbackData, "_")
	if len(parts) < 6 {
		slog.Error("Invalid callback data format", "data", callbackData)
		return
	}

	paymentMethod := parts[4] // auto_renewal_setup_payment_yookasa_1m -> yookasa
	tariffCode := parts[5]    // auto_renewal_setup_payment_yookasa_1m -> 1m

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Получаем информацию о тарифе
	tariff, err := h.tariffRepository.GetByCode(ctx, tariffCode)
	if err != nil || tariff == nil {
		slog.Error("Tariff not found", "tariff_code", tariffCode, "error", err)
		return
	}

	// Показываем подтверждение настройки автопродления
	messageText := h.buildAutoRenewalConfirmationText(tariff, paymentMethod, langCode)
	keyboard := h.createAutoRenewalConfirmationKeyboard(langCode, paymentMethod, tariffCode)

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:      callback.Chat.ID,
		MessageID:   callback.ID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// HandleAutoRenewalConfirm - подтверждает настройку автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalConfirm(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	// Извлекаем данные из callback data: auto_renewal_confirm_{method}_{tariffCode}
	callbackData := update.CallbackQuery.Data
	parts := strings.Split(callbackData, "_")
	if len(parts) < 4 {
		slog.Error("Invalid callback data format", "data", callbackData)
		return
	}

	paymentMethod := parts[3]
	tariffCode := parts[4]

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Создаем пустые сохраненные данные (будут заполнены при первом платеже)
	savedPaymentData := make(map[string]any)

	// Включаем автопродление
	err = h.autoRenewalService.EnableAutoRenewal(ctx, customer.ID, paymentMethod, tariffCode, savedPaymentData)
	if err != nil {
		slog.Error("Error enabling auto renewal", "error", err)

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      h.translation.GetText(langCode, "auto_renewal_setup_error"),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackConnect}},
				},
			},
		})
		return
	}

	// Показываем сообщение об успешной настройке
	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      h.translation.GetText(langCode, "auto_renewal_setup_success"),
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{{Text: h.translation.GetText(langCode, "back_to_subscription_button"), CallbackData: CallbackConnect}},
			},
		},
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// createTariffSelectionKeyboard - создает клавиатуру для выбора тарифа
func (h *AutoRenewalHandler) createTariffSelectionKeyboard(ctx context.Context, langCode string) models.InlineKeyboardMarkup {
	// Получаем все активные тарифы
	tariffs, err := h.tariffRepository.GetAll(ctx, true)
	if err != nil {
		slog.Error("Error getting tariffs", "error", err)
		return models.InlineKeyboardMarkup{}
	}

	var keyboard [][]models.InlineKeyboardButton

	// Добавляем кнопки для каждого тарифа
	for _, tariff := range tariffs {
		keyboard = append(keyboard, []models.InlineKeyboardButton{
			{
				Text:         fmt.Sprintf("%s - %d₽", tariff.Title, tariff.PriceRUB),
				CallbackData: fmt.Sprintf("auto_renewal_select_tariff_%s", tariff.Code),
			},
		})
	}

	// Кнопка назад
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "back_button"),
			CallbackData: "auto_renewal_toggle",
		},
	})

	return models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}

// createPaymentMethodSelectionKeyboardForSetup - создает клавиатуру для выбора способа оплаты при настройке
func (h *AutoRenewalHandler) createPaymentMethodSelectionKeyboardForSetup(langCode, tariffCode string) models.InlineKeyboardMarkup {
	var keyboard [][]models.InlineKeyboardButton

	// Добавляем доступные способы оплаты
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "payment_method_card"),
			CallbackData: fmt.Sprintf("auto_renewal_setup_payment_yookasa_%s", tariffCode),
		},
	})

	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "payment_method_crypto"),
			CallbackData: fmt.Sprintf("auto_renewal_setup_payment_cryptopay_%s", tariffCode),
		},
	})

	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "payment_method_stars"),
			CallbackData: fmt.Sprintf("auto_renewal_setup_payment_telegram_%s", tariffCode),
		},
	})

	// Кнопка назад
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "back_button"),
			CallbackData: "auto_renewal_setup_start",
		},
	})

	return models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}

// hasSavedPaymentMethod проверяет наличие сохраненных платежных данных
func (h *AutoRenewalHandler) hasSavedPaymentMethod(settings *database.AutoRenewalSettings) bool {
	if settings == nil {
		slog.Debug("No saved payment method: settings is nil")
		return false
	}

	if settings.SavedPaymentData == nil {
		slog.Debug("No saved payment method: SavedPaymentData is nil",
			"customer_id", settings.CustomerID,
			"payment_method", settings.PaymentMethod)
		return false
	}

	slog.Debug("Checking saved payment method",
		"customer_id", settings.CustomerID,
		"payment_method", settings.PaymentMethod,
		"saved_data_keys", getMapKeys(settings.SavedPaymentData),
		"data_count", len(settings.SavedPaymentData))

	// Проверяем наличие payment_method_id для YooKassa
	if settings.PaymentMethod == "yookasa" {
		if paymentMethodID, exists := settings.SavedPaymentData["payment_method_id"]; exists {
			if paymentMethodIDStr, ok := paymentMethodID.(string); ok && paymentMethodIDStr != "" {
				slog.Info("Found saved YooKassa payment method",
					"customer_id", settings.CustomerID,
					"payment_method_id", paymentMethodIDStr,
					"payment_type", settings.SavedPaymentData["payment_type"])

				// Дополнительная проверка валидности UUID
				if _, err := uuid.Parse(paymentMethodIDStr); err != nil {
					slog.Error("Invalid YooKassa payment_method_id format",
						"customer_id", settings.CustomerID,
						"payment_method_id", paymentMethodIDStr,
						"error", err)
					return false
				}

				return true
			} else {
				slog.Debug("YooKassa payment_method_id exists but invalid type or empty",
					"customer_id", settings.CustomerID,
					"value", paymentMethodID,
					"type", fmt.Sprintf("%T", paymentMethodID))
			}
		} else {
			slog.Debug("No payment_method_id key found in saved data",
				"customer_id", settings.CustomerID,
				"available_keys", getMapKeys(settings.SavedPaymentData))
		}
		slog.Debug("No valid YooKassa payment_method_id found", "customer_id", settings.CustomerID)
	}

	// Проверяем наличие subscription_id для Telegram Stars
	if settings.PaymentMethod == "telegram" {
		if subscriptionID, exists := settings.SavedPaymentData["subscription_id"]; exists {
			if subscriptionIDStr, ok := subscriptionID.(string); ok && subscriptionIDStr != "" {
				slog.Info("Found saved Telegram Stars subscription",
					"customer_id", settings.CustomerID,
					"subscription_id", subscriptionIDStr)
				return true
			}
		}
		slog.Debug("No valid Telegram Stars subscription_id found", "customer_id", settings.CustomerID)
	}

	// Проверяем другие способы оплаты
	switch settings.PaymentMethod {
	case "cryptopay":
		if cryptoData, exists := settings.SavedPaymentData["crypto_invoice_id"]; exists && cryptoData != nil {
			slog.Info("Found saved CryptoPay data", "customer_id", settings.CustomerID)
			return true
		}
	case "tribute":
		if tributeData, exists := settings.SavedPaymentData["tribute_data"]; exists && tributeData != nil {
			slog.Info("Found saved Tribute data", "customer_id", settings.CustomerID)
			return true
		}
	}

	slog.Debug("No saved payment method found for payment method",
		"customer_id", settings.CustomerID,
		"payment_method", settings.PaymentMethod)
	return false
}

// getCustomerAndSettings - общая функция для получения customer и настроек автопродления
func (h *AutoRenewalHandler) getCustomerAndSettings(ctx context.Context, chatID int64) (*database.Customer, *database.AutoRenewalSettings, error) {
	customer, err := h.customerRepository.FindByTelegramId(ctx, chatID)
	if err != nil {
		return nil, nil, fmt.Errorf("error finding customer: %w", err)
	}
	if customer == nil {
		return nil, nil, fmt.Errorf("customer not found for chat_id: %d", chatID)
	}

	// Получаем текущие настройки автопродления
	settings, err := h.autoRenewalService.GetAutoRenewalSettings(ctx, customer.ID)
	if err != nil && err.Error() != "no rows in result set" {
		return customer, nil, fmt.Errorf("error getting auto renewal settings: %w", err)
	}

	return customer, settings, nil
}

// buildAutoRenewalInterface - общая функция для построения интерфейса автопродления
func (h *AutoRenewalHandler) buildAutoRenewalInterface(ctx context.Context, customer *database.Customer, settings *database.AutoRenewalSettings, langCode, source string) (string, models.InlineKeyboardMarkup) {
	slog.Debug("Building auto renewal interface",
		"customer_id", customer.ID,
		"has_settings", settings != nil,
		"settings_enabled", settings != nil && settings.Enabled,
		"source", source)

	var messageText string
	var keyboard models.InlineKeyboardMarkup

	if settings == nil || !settings.Enabled {
		// Автопродление выключено, проверяем наличие сохраненных платежных данных
		if settings != nil && h.hasSavedPaymentMethod(settings) {
			// У пользователя есть сохраненные платежные данные
			slog.Info("User has saved payment method, showing reenable options",
				"customer_id", customer.ID,
				"payment_method", settings.PaymentMethod,
				"source", source)

			messageText = h.buildAutoRenewalReenableText(settings, langCode)
			keyboard = h.createAutoRenewalReenableKeyboard(langCode, settings)
		} else {
			// Первичная настройка автопродления
			slog.Info("No saved payment method found, showing setup options",
				"customer_id", customer.ID,
				"source", source)

			if source == "setup" {
				// Для HandleAutoRenewalSetupStart показываем выбор тарифов
				messageText = h.translation.GetText(langCode, "auto_renewal_select_tariff_message")
				keyboard = h.createTariffSelectionKeyboard(ctx, langCode)
			} else {
				// Для HandleAutoRenewalToggle показываем общее сообщение настройки
				messageText = h.translation.GetText(langCode, "auto_renewal_setup_message")
				keyboard = h.createAutoRenewalSetupKeyboard(langCode)
			}
		}
	} else {
		// Автопродление включено, показываем опции для управления
		tariff, _ := h.tariffRepository.GetByCode(ctx, settings.TariffCode)
		messageText = h.buildAutoRenewalStatusText(settings, tariff, langCode)
		keyboard = h.createAutoRenewalManageKeyboard(langCode)
	}

	return messageText, keyboard
}

// getMapKeys - вспомогательная функция для получения ключей из map для логирования
func getMapKeys(m map[string]any) []string {
	if m == nil {
		return nil
	}
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// buildAutoRenewalReenableText создает текст сообщения для повторного включения автопродления
func (h *AutoRenewalHandler) buildAutoRenewalReenableText(settings *database.AutoRenewalSettings, langCode string) string {
	baseText := h.translation.GetText(langCode, "auto_renewal_reenable_message")

	// Добавляем информацию о сохраненном методе оплаты
	var paymentMethodText string
	switch settings.PaymentMethod {
	case "yookasa":
		paymentMethodText = h.translation.GetText(langCode, "payment_method_card")
	case "telegram":
		paymentMethodText = h.translation.GetText(langCode, "payment_method_stars")
	case "cryptopay":
		paymentMethodText = h.translation.GetText(langCode, "payment_method_crypto")
	default:
		paymentMethodText = settings.PaymentMethod
	}

	return fmt.Sprintf("%s\n\n💳 %s: %s", baseText,
		h.translation.GetText(langCode, "saved_payment_method"), paymentMethodText)
}

// createAutoRenewalReenableKeyboard создает клавиатуру для повторного включения автопродления с сохраненными данными
func (h *AutoRenewalHandler) createAutoRenewalReenableKeyboard(langCode string, settings *database.AutoRenewalSettings) models.InlineKeyboardMarkup {
	var keyboard [][]models.InlineKeyboardButton

	// Кнопка для включения с сохраненным методом
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "auto_renewal_use_saved_method"),
			CallbackData: fmt.Sprintf("auto_renewal_reenable_saved_%s", settings.TariffCode),
		},
	})

	// Кнопка для настройки нового метода
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "auto_renewal_setup_new_method"),
			CallbackData: "auto_renewal_setup_new",
		},
	})

	// Кнопка назад
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "button_back"),
			CallbackData: "payment_settings",
		},
	})

	return models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}

// HandleAutoRenewalReenableWithSaved - обрабатывает повторное включение автопродления с сохраненными данными
func (h *AutoRenewalHandler) HandleAutoRenewalReenableWithSaved(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	slog.Info("HandleAutoRenewalReenableWithSaved called",
		"chat_id", callback.Chat.ID,
		"user_id", update.CallbackQuery.From.ID,
		"callback_data", update.CallbackQuery.Data)

	// Извлекаем tariff_code из callback data: auto_renewal_reenable_saved_{tariffCode}
	callbackData := update.CallbackQuery.Data
	parts := strings.Split(callbackData, "_")
	if len(parts) < 4 {
		slog.Error("Invalid callback data format", "data", callbackData, "parts", parts)
		return
	}

	tariffCode := parts[3] // auto_renewal_reenable_saved_1m -> 1m
	slog.Info("Parsed tariff code from callback data", "tariff_code", tariffCode, "parts", parts)

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Получаем текущие настройки автопродления
	settings, err := h.autoRenewalService.GetAutoRenewalSettings(ctx, customer.ID)
	if err != nil {
		slog.Error("Error getting auto renewal settings", "error", err)
		return
	}

	if settings == nil || !h.hasSavedPaymentMethod(settings) {
		slog.Error("No saved payment method found", "customer_id", customer.ID)

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      h.translation.GetText(langCode, "auto_renewal_no_saved_method_error"),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "button_back"), CallbackData: "payment_settings"}},
				},
			},
		})
		return
	}

	// Включаем автопродление с сохраненными данными
	err = h.autoRenewalService.EnableAutoRenewal(ctx, customer.ID, settings.PaymentMethod, tariffCode, settings.SavedPaymentData)
	if err != nil {
		slog.Error("Error enabling auto renewal with saved method", "error", err, "customer_id", customer.ID)

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      h.translation.GetText(langCode, "auto_renewal_enable_error"),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "button_back"), CallbackData: "payment_settings"}},
				},
			},
		})
		return
	}

	slog.Info("Auto renewal enabled with saved payment method",
		"customer_id", customer.ID,
		"payment_method", settings.PaymentMethod,
		"tariff_code", tariffCode)

	// Получаем информацию о тарифе для отображения
	tariff, err := h.tariffRepository.GetByCode(ctx, tariffCode)
	if err != nil {
		slog.Error("Error getting tariff", "error", err)
		return
	}

	// Показываем сообщение об успешном включении
	messageText := fmt.Sprintf(
		h.translation.GetText(langCode, "auto_renewal_enabled_with_saved_method"),
		tariff.Title,
		h.translation.GetText(langCode, "payment_method_"+settings.PaymentMethod),
	)

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      messageText,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{{Text: h.translation.GetText(langCode, "button_back"), CallbackData: "payment_settings"}},
			},
		},
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// buildAutoRenewalConfirmationText - формирует текст подтверждения настройки автопродления
func (h *AutoRenewalHandler) buildAutoRenewalConfirmationText(tariff *database.Tariff, paymentMethod, langCode string) string {
	var price int
	var currency string
	switch paymentMethod {
	case "yookasa", "cryptopay", "tribute":
		price = tariff.PriceRUB
		currency = "₽"
	case "telegram":
		price = tariff.PriceStars
		currency = "⭐"
	default:
		price = tariff.PriceRUB
		currency = "₽"
	}

	paymentMethodName := h.getPaymentMethodName(paymentMethod, langCode)

	return fmt.Sprintf(
		h.translation.GetText(langCode, "auto_renewal_confirmation_message"),
		tariff.Title,
		price,
		currency,
		paymentMethodName,
	)
}

// createAutoRenewalConfirmationKeyboard - создает клавиатуру подтверждения настройки автопродления
func (h *AutoRenewalHandler) createAutoRenewalConfirmationKeyboard(langCode, paymentMethod, tariffCode string) models.InlineKeyboardMarkup {
	return models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text:         h.translation.GetText(langCode, "confirm_button"),
					CallbackData: fmt.Sprintf("auto_renewal_confirm_%s_%s", paymentMethod, tariffCode),
				},
			},
			{
				{
					Text:         h.translation.GetText(langCode, "back_button"),
					CallbackData: fmt.Sprintf("auto_renewal_select_tariff_%s", tariffCode),
				},
			},
		},
	}
}

// HandleCryptoPayQuickPayment - обрабатывает быструю оплату через CryptoPay
func (h *AutoRenewalHandler) HandleCryptoPayQuickPayment(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	// Парсим callback data: cryptopay_quick_payment:customerID:tariffCode
	callbackData := update.CallbackQuery.Data
	parts := strings.Split(callbackData, ":")
	if len(parts) != 3 {
		slog.Error("Invalid callback data format for CryptoPay quick payment", "data", callbackData)
		return
	}

	customerIDStr := parts[1]
	tariffCode := parts[2]

	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		slog.Error("Invalid customer ID in callback data", "customer_id", customerIDStr, "error", err)
		return
	}

	// Получаем информацию о клиенте
	customer, err := h.customerRepository.FindById(ctx, customerID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "customer_id", customerID)
		return
	}

	// Получаем информацию о тарифе
	tariff, err := h.tariffRepository.GetByCode(ctx, tariffCode)
	if err != nil || tariff == nil {
		slog.Error("Tariff not found", "tariff_code", tariffCode, "error", err)
		return
	}

	// Показываем сообщение о создании инвойса
	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      h.translation.GetText(langCode, "cryptopay_quick_payment_processing"),
		ParseMode: models.ParseModeHTML,
	})
	if err != nil {
		slog.Error("Error editing message to show processing", "error", err)
	}

	// Создаем новый инвойс CryptoPay
	url, purchaseID, err := h.paymentService.CreatePurchaseByTariff(ctx, tariff, customer, database.InvoiceTypeCrypto, "")
	if err != nil {
		slog.Error("Error creating CryptoPay invoice", "error", err)

		// Показываем сообщение об ошибке
		_, editErr := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      h.translation.GetText(langCode, "cryptopay_quick_payment_error"),
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackConnect}},
				},
			},
		})
		if editErr != nil {
			slog.Error("Error editing message to show error", "error", editErr)
		}
		return
	}

	// Формируем сообщение с ссылкой на оплату
	messageText := fmt.Sprintf(
		h.translation.GetText(langCode, "cryptopay_auto_renewal_notification"),
		tariff.Title,
		tariff.PriceRUB,
		tariff.GetMonthsFromCode(),
	)

	// Создаем клавиатуру с кнопкой оплаты
	keyboard := models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text: h.translation.GetText(langCode, "cryptopay_payment_button"),
					URL:  url,
				},
			},
			{
				{
					Text:         h.translation.GetText(langCode, "back_button"),
					CallbackData: CallbackConnect,
				},
			},
		},
	}

	// Обновляем сообщение с инвойсом
	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:      callback.Chat.ID,
		MessageID:   callback.ID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Error editing message with CryptoPay invoice", "error", err)
	}

	slog.Info("CryptoPay quick payment invoice created",
		"customer_id", customerID,
		"tariff_code", tariffCode,
		"purchase_id", purchaseID,
		"url", url)
}
