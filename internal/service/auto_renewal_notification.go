package service

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/translation"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// AutoRenewalNotificationService - сервис для отправки уведомлений об автопродлении
type AutoRenewalNotificationService struct {
	autoRenewalRepository *database.AutoRenewalRepository
	tariffRepository      *database.TariffRepository
	telegramBot           *bot.Bot
	translation           *translation.Manager
}

// NewAutoRenewalNotificationService - создает новый экземпляр сервиса уведомлений об автопродлении
func NewAutoRenewalNotificationService(
	autoRenewalRepository *database.AutoRenewalRepository,
	tariffRepository *database.TariffRepository,
	telegramBot *bot.Bot,
	translation *translation.Manager,
) *AutoRenewalNotificationService {
	return &AutoRenewalNotificationService{
		autoRenewalRepository: autoRenewalRepository,
		tariffRepository:      tariffRepository,
		telegramBot:           telegramBot,
		translation:           translation,
	}
}

// SendAllAutoRenewalNotifications - отправляет все типы уведомлений об автопродлении
func (s *AutoRenewalNotificationService) SendAllAutoRenewalNotifications(ctx context.Context) error {
	slog.Info("Starting auto renewal notifications check")

	// Определяем все типы уведомлений с их временными окнами
	notificationTypes := []struct {
		Type     database.AutoRenewalNotificationType
		Duration time.Duration
		Name     string
	}{
		{database.AutoRenewalNotification3d, 72 * time.Hour, "3d"},
		{database.AutoRenewalNotification1d, 24 * time.Hour, "1d"},
		{database.AutoRenewalNotification6h, 6 * time.Hour, "6h"},
		{database.AutoRenewalNotification1h, 1 * time.Hour, "1h"},
		{database.AutoRenewalNotificationExpired, 1 * time.Hour, "expired"}, // 1 час после истечения
	}

	var totalSent int
	var totalErrors int

	// Обрабатываем каждый тип уведомления
	for _, notifType := range notificationTypes {
		customers, err := s.autoRenewalRepository.GetCustomersForNotification(ctx, notifType.Type, notifType.Duration)
		if err != nil {
			slog.Error("Failed to get customers for notification",
				"type", notifType.Name,
				"error", err)
			totalErrors++
			continue
		}

		if len(customers) == 0 {
			slog.Debug("No customers found for notification type", "type", notifType.Name)
			continue
		}

		slog.Info("Processing auto renewal notifications",
			"type", notifType.Name,
			"count", len(customers))

		// Отправляем уведомления для каждого клиента
		for _, customer := range customers {
			err := s.sendNotificationByType(ctx, customer, notifType.Type)
			if err != nil {
				slog.Error("Failed to send auto renewal notification",
					"type", notifType.Name,
					"customer_id", customer.ID,
					"telegram_id", customer.TelegramID,
					"error", err)
				totalErrors++
				continue
			}

			// Записываем факт отправки уведомления
			notification := &database.AutoRenewalNotification{
				CustomerID:            customer.ID,
				NotificationType:      notifType.Type,
				SubscriptionExpiresAt: *customer.ExpireAt,
			}

			err = s.autoRenewalRepository.CreateNotification(ctx, notification)
			if err != nil {
				slog.Error("Failed to create notification record",
					"type", notifType.Name,
					"customer_id", customer.ID,
					"error", err)
			}

			totalSent++
			slog.Debug("Auto renewal notification sent successfully",
				"type", notifType.Name,
				"customer_id", customer.ID,
				"telegram_id", customer.TelegramID)
		}
	}

	slog.Info("Auto renewal notifications processing completed",
		"total_sent", totalSent,
		"total_errors", totalErrors)

	if totalErrors > 0 {
		return fmt.Errorf("completed with %d errors out of %d total notifications", totalErrors, totalSent+totalErrors)
	}

	return nil
}

// sendNotificationByType - отправляет уведомление определенного типа
func (s *AutoRenewalNotificationService) sendNotificationByType(ctx context.Context, customer database.Customer, notificationType database.AutoRenewalNotificationType) error {
	switch notificationType {
	case database.AutoRenewalNotification3d:
		return s.send3dNotification(ctx, customer)
	case database.AutoRenewalNotification1d:
		return s.send1dNotification(ctx, customer)
	case database.AutoRenewalNotification6h:
		return s.send6hNotification(ctx, customer)
	case database.AutoRenewalNotification1h:
		return s.send1hNotification(ctx, customer)
	case database.AutoRenewalNotificationExpired:
		return s.sendExpiredNotification(ctx, customer)
	default:
		return fmt.Errorf("unknown notification type: %s", notificationType)
	}
}

// send24hNotification - отправляет уведомление за 24 часа до автопродления
func (s *AutoRenewalNotificationService) send24hNotification(ctx context.Context, customer database.Customer) error {
	// Получаем настройки автопродления для пользователя
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customer.ID)
	if err != nil {
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	// Получаем информацию о тарифе
	tariff, err := s.tariffRepository.GetByCode(ctx, settings.TariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}

	// Определяем стоимость в зависимости от способа оплаты
	var price int
	var currency string
	switch settings.PaymentMethod {
	case "yookasa", "cryptopay", "tribute":
		price = tariff.PriceRUB
		currency = "₽"
	case "telegram":
		price = tariff.PriceStars
		currency = "⭐"
	default:
		price = tariff.PriceRUB
		currency = "₽"
	}

	expireDate := customer.ExpireAt.Format("02.01.2006 15:04")

	// Формируем текст уведомления в зависимости от способа оплаты
	var messageText string
	if settings.PaymentMethod == "cryptopay" {
		// Специальное уведомление для CryptoPay
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "cryptopay_24h_notification"),
			tariff.Title,
			price,
			expireDate,
		)
	} else {
		// Стандартное уведомление для других способов оплаты
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "auto_renewal_24h_notification"),
			tariff.Title,
			expireDate,
			price,
			currency,
			s.getPaymentMethodName(settings.PaymentMethod, customer.Language),
		)
	}

	// Создаем клавиатуру с действиями
	keyboard := s.create24hNotificationKeyboard(customer.Language, settings.PaymentMethod, customer.ID, settings.TariffCode)

	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	return err
}

// send12hNotification - отправляет уведомление за 12 часов до автопродления
func (s *AutoRenewalNotificationService) send12hNotification(ctx context.Context, customer database.Customer) error {
	// Получаем настройки автопродления для пользователя
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customer.ID)
	if err != nil {
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	// Получаем информацию о тарифе
	tariff, err := s.tariffRepository.GetByCode(ctx, settings.TariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}

	// Определяем стоимость в зависимости от способа оплаты
	var price int
	var currency string
	switch settings.PaymentMethod {
	case "yookasa", "cryptopay", "tribute":
		price = tariff.PriceRUB
		currency = "₽"
	case "telegram":
		price = tariff.PriceStars
		currency = "⭐"
	default:
		price = tariff.PriceRUB
		currency = "₽"
	}

	expireDate := customer.ExpireAt.Format("02.01.2006 15:04")

	// Формируем текст уведомления в зависимости от способа оплаты
	var messageText string
	if settings.PaymentMethod == "cryptopay" {
		// Специальное уведомление для CryptoPay с приблизительной стоимостью в USDT
		usdtAmount := price / 100 // Приблизительный курс 1 USDT = 100 RUB
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "cryptopay_12h_notification"),
			tariff.Title,
			price,
			expireDate,
			usdtAmount,
		)
	} else {
		// Стандартное уведомление для других способов оплаты
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "auto_renewal_12h_notification"),
			tariff.Title,
			expireDate,
			price,
			currency,
			s.getPaymentMethodName(settings.PaymentMethod, customer.Language),
		)
	}

	// Создаем клавиатуру с действиями
	keyboard := s.create12hNotificationKeyboard(customer.Language, settings.PaymentMethod, customer.ID, settings.TariffCode)

	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	return err
}

// create24hNotificationKeyboard - создает клавиатуру для уведомления за 24 часа
func (s *AutoRenewalNotificationService) create24hNotificationKeyboard(langCode, paymentMethod string, customerID int64, tariffCode string) models.InlineKeyboardMarkup {
	buttons := [][]models.InlineKeyboardButton{
		{
			{
				Text:         s.translation.GetText(langCode, "manual_renewal_button"),
				CallbackData: "buy",
			},
		},
	}

	// Добавляем кнопку быстрой оплаты для CryptoPay
	if paymentMethod == "cryptopay" {
		quickPaymentButton := []models.InlineKeyboardButton{
			{
				Text:         s.translation.GetText(langCode, "cryptopay_quick_payment_button"),
				CallbackData: fmt.Sprintf("cryptopay_quick_payment:%d:%s", customerID, tariffCode),
			},
		}
		buttons = append(buttons, quickPaymentButton)
	}

	// Добавляем остальные кнопки
	buttons = append(buttons, []models.InlineKeyboardButton{
		{
			Text:         s.translation.GetText(langCode, "change_payment_method_button"),
			CallbackData: "auto_renewal_change_payment",
		},
	})

	buttons = append(buttons, []models.InlineKeyboardButton{
		{
			Text:         s.translation.GetText(langCode, "disable_auto_renewal_button"),
			CallbackData: "auto_renewal_disable",
		},
	})

	return models.InlineKeyboardMarkup{
		InlineKeyboard: buttons,
	}
}

// create12hNotificationKeyboard - создает клавиатуру для уведомления за 12 часов
func (s *AutoRenewalNotificationService) create12hNotificationKeyboard(langCode, paymentMethod string, customerID int64, tariffCode string) models.InlineKeyboardMarkup {
	buttons := [][]models.InlineKeyboardButton{
		{
			{
				Text:         s.translation.GetText(langCode, "manual_renewal_button"),
				CallbackData: "buy",
			},
		},
	}

	// Добавляем кнопку быстрой оплаты для CryptoPay
	if paymentMethod == "cryptopay" {
		quickPaymentButton := []models.InlineKeyboardButton{
			{
				Text:         s.translation.GetText(langCode, "cryptopay_quick_payment_button"),
				CallbackData: fmt.Sprintf("cryptopay_quick_payment:%d:%s", customerID, tariffCode),
			},
		}
		buttons = append(buttons, quickPaymentButton)
	}

	// Добавляем кнопку отключения автопродления
	buttons = append(buttons, []models.InlineKeyboardButton{
		{
			Text:         s.translation.GetText(langCode, "disable_auto_renewal_button"),
			CallbackData: "auto_renewal_disable",
		},
	})

	return models.InlineKeyboardMarkup{
		InlineKeyboard: buttons,
	}
}

// getPaymentMethodName - возвращает локализованное название способа оплаты
func (s *AutoRenewalNotificationService) getPaymentMethodName(paymentMethod, langCode string) string {
	switch paymentMethod {
	case "yookasa":
		return s.translation.GetText(langCode, "payment_method_card")
	case "cryptopay":
		return s.translation.GetText(langCode, "payment_method_cryptopay")
	case "telegram":
		return s.translation.GetText(langCode, "payment_method_stars")
	case "tribute":
		return s.translation.GetText(langCode, "payment_method_tribute")
	default:
		return paymentMethod
	}
}

// send3dNotification - отправляет уведомление за 3 дня до автопродления
func (s *AutoRenewalNotificationService) send3dNotification(ctx context.Context, customer database.Customer) error {
	// Получаем настройки автопродления для пользователя
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customer.ID)
	if err != nil {
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	// Получаем информацию о тарифе
	tariff, err := s.tariffRepository.GetByCode(ctx, settings.TariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}

	// Определяем цену в зависимости от способа оплаты
	var price int
	if settings.PaymentMethod == "telegram_stars" {
		price = tariff.PriceStars
	} else {
		price = tariff.PriceRUB
	}

	expireDate := customer.ExpireAt.Format("02.01.2006 15:04")

	// Формируем текст уведомления в зависимости от способа оплаты
	var messageText string
	if settings.PaymentMethod == "cryptopay" {
		// Специальное уведомление для CryptoPay
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "cryptopay_3d_notification"),
			tariff.Title,
			price,
			expireDate,
		)
	} else {
		// Стандартное уведомление для других способов оплаты
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "auto_renewal_24h_notification"),
			tariff.Title,
			expireDate,
			price,
			s.getCurrency(settings.PaymentMethod),
			s.getPaymentMethodName(settings.PaymentMethod, customer.Language),
		)
	}

	// Создаем клавиатуру с действиями
	keyboard := s.create3dNotificationKeyboard(customer.Language, settings.PaymentMethod, customer.ID, settings.TariffCode)

	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	return err
}

// send1dNotification - отправляет уведомление за 1 день до автопродления
func (s *AutoRenewalNotificationService) send1dNotification(ctx context.Context, customer database.Customer) error {
	// Получаем настройки автопродления для пользователя
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customer.ID)
	if err != nil {
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	// Получаем информацию о тарифе
	tariff, err := s.tariffRepository.GetByCode(ctx, settings.TariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}

	// Определяем цену в зависимости от способа оплаты
	var price int
	if settings.PaymentMethod == "telegram_stars" {
		price = tariff.PriceStars
	} else {
		price = tariff.PriceRUB
	}

	expireDate := customer.ExpireAt.Format("02.01.2006 15:04")

	// Формируем текст уведомления в зависимости от способа оплаты
	var messageText string
	if settings.PaymentMethod == "cryptopay" {
		// Специальное уведомление для CryptoPay
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "cryptopay_1d_notification"),
			tariff.Title,
			price,
			expireDate,
		)
	} else {
		// Стандартное уведомление для других способов оплаты
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "auto_renewal_12h_notification"),
			tariff.Title,
			expireDate,
			price,
			s.getCurrency(settings.PaymentMethod),
			s.getPaymentMethodName(settings.PaymentMethod, customer.Language),
		)
	}

	// Создаем клавиатуру с действиями
	keyboard := s.create1dNotificationKeyboard(customer.Language, settings.PaymentMethod, customer.ID, settings.TariffCode)

	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	return err
}

// send6hNotification - отправляет уведомление за 6 часов до автопродления
func (s *AutoRenewalNotificationService) send6hNotification(ctx context.Context, customer database.Customer) error {
	// Получаем настройки автопродления для пользователя
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customer.ID)
	if err != nil {
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	// Получаем информацию о тарифе
	tariff, err := s.tariffRepository.GetByCode(ctx, settings.TariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}

	// Определяем цену в зависимости от способа оплаты
	var price int
	if settings.PaymentMethod == "telegram_stars" {
		price = tariff.PriceStars
	} else {
		price = tariff.PriceRUB
	}

	expireDate := customer.ExpireAt.Format("02.01.2006 15:04")

	// Формируем текст уведомления в зависимости от способа оплаты
	var messageText string
	if settings.PaymentMethod == "cryptopay" {
		// Специальное уведомление для CryptoPay
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "cryptopay_6h_notification"),
			tariff.Title,
			price,
			expireDate,
		)
	} else {
		// Стандартное уведомление для других способов оплаты
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "auto_renewal_12h_notification"),
			tariff.Title,
			expireDate,
			price,
			s.getCurrency(settings.PaymentMethod),
			s.getPaymentMethodName(settings.PaymentMethod, customer.Language),
		)
	}

	// Создаем клавиатуру с действиями
	keyboard := s.create6hNotificationKeyboard(customer.Language, settings.PaymentMethod, customer.ID, settings.TariffCode)

	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	return err
}

// send1hNotification - отправляет уведомление за 1 час до автопродления
func (s *AutoRenewalNotificationService) send1hNotification(ctx context.Context, customer database.Customer) error {
	// Получаем настройки автопродления для пользователя
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customer.ID)
	if err != nil {
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	// Получаем информацию о тарифе
	tariff, err := s.tariffRepository.GetByCode(ctx, settings.TariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}

	// Определяем цену в зависимости от способа оплаты
	var price int
	if settings.PaymentMethod == "telegram_stars" {
		price = tariff.PriceStars
	} else {
		price = tariff.PriceRUB
	}

	expireDate := customer.ExpireAt.Format("02.01.2006 15:04")

	// Формируем текст уведомления в зависимости от способа оплаты
	var messageText string
	if settings.PaymentMethod == "cryptopay" {
		// Специальное уведомление для CryptoPay
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "cryptopay_1h_notification"),
			tariff.Title,
			price,
			expireDate,
		)
	} else {
		// Стандартное уведомление для других способов оплаты
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "auto_renewal_12h_notification"),
			tariff.Title,
			expireDate,
			price,
			s.getCurrency(settings.PaymentMethod),
			s.getPaymentMethodName(settings.PaymentMethod, customer.Language),
		)
	}

	// Создаем клавиатуру с действиями
	keyboard := s.create1hNotificationKeyboard(customer.Language, settings.PaymentMethod, customer.ID, settings.TariffCode)

	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	return err
}

// sendExpiredNotification - отправляет уведомление после истечения подписки
func (s *AutoRenewalNotificationService) sendExpiredNotification(ctx context.Context, customer database.Customer) error {
	// Получаем настройки автопродления для пользователя
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customer.ID)
	if err != nil {
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	// Получаем информацию о тарифе
	tariff, err := s.tariffRepository.GetByCode(ctx, settings.TariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}

	// Определяем цену в зависимости от способа оплаты
	var price int
	if settings.PaymentMethod == "telegram_stars" {
		price = tariff.PriceStars
	} else {
		price = tariff.PriceRUB
	}

	expireDate := customer.ExpireAt.Format("02.01.2006 15:04")

	// Формируем текст уведомления в зависимости от способа оплаты
	var messageText string
	if settings.PaymentMethod == "cryptopay" {
		// Специальное уведомление для CryptoPay
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "cryptopay_expired_notification"),
			tariff.Title,
			price,
			expireDate,
		)
	} else {
		// Стандартное уведомление для других способов оплаты
		messageText = fmt.Sprintf(
			s.translation.GetText(customer.Language, "auto_renewal_12h_notification"),
			tariff.Title,
			expireDate,
			price,
			s.getCurrency(settings.PaymentMethod),
			s.getPaymentMethodName(settings.PaymentMethod, customer.Language),
		)
	}

	// Создаем клавиатуру с действиями
	keyboard := s.createExpiredNotificationKeyboard(customer.Language, settings.PaymentMethod, customer.ID, settings.TariffCode)

	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	return err
}

// getCurrency - возвращает валюту для способа оплаты
func (s *AutoRenewalNotificationService) getCurrency(paymentMethod string) string {
	switch paymentMethod {
	case "telegram_stars":
		return "⭐"
	default:
		return "₽"
	}
}

// create3dNotificationKeyboard - создает клавиатуру для уведомления за 3 дня
func (s *AutoRenewalNotificationService) create3dNotificationKeyboard(langCode, paymentMethod string, customerID int64, tariffCode string) models.InlineKeyboardMarkup {
	buttons := [][]models.InlineKeyboardButton{
		{
			{
				Text:         s.translation.GetText(langCode, "manual_renewal_button"),
				CallbackData: "buy",
			},
		},
	}

	// Добавляем кнопку быстрой оплаты для CryptoPay
	if paymentMethod == "cryptopay" {
		quickPaymentButton := []models.InlineKeyboardButton{
			{
				Text:         s.translation.GetText(langCode, "cryptopay_quick_payment_button"),
				CallbackData: fmt.Sprintf("cryptopay_quick_payment:%d:%s", customerID, tariffCode),
			},
		}
		buttons = append(buttons, quickPaymentButton)
	}

	// Добавляем кнопку отключения автопродления
	buttons = append(buttons, []models.InlineKeyboardButton{
		{
			Text:         s.translation.GetText(langCode, "disable_auto_renewal_button"),
			CallbackData: "auto_renewal_disable",
		},
	})

	return models.InlineKeyboardMarkup{
		InlineKeyboard: buttons,
	}
}

// create1dNotificationKeyboard - создает клавиатуру для уведомления за 1 день
func (s *AutoRenewalNotificationService) create1dNotificationKeyboard(langCode, paymentMethod string, customerID int64, tariffCode string) models.InlineKeyboardMarkup {
	buttons := [][]models.InlineKeyboardButton{
		{
			{
				Text:         s.translation.GetText(langCode, "manual_renewal_button"),
				CallbackData: "buy",
			},
		},
	}

	// Добавляем кнопку быстрой оплаты для CryptoPay
	if paymentMethod == "cryptopay" {
		quickPaymentButton := []models.InlineKeyboardButton{
			{
				Text:         s.translation.GetText(langCode, "cryptopay_quick_payment_button"),
				CallbackData: fmt.Sprintf("cryptopay_quick_payment:%d:%s", customerID, tariffCode),
			},
		}
		buttons = append(buttons, quickPaymentButton)
	}

	// Добавляем кнопку отключения автопродления
	buttons = append(buttons, []models.InlineKeyboardButton{
		{
			Text:         s.translation.GetText(langCode, "disable_auto_renewal_button"),
			CallbackData: "auto_renewal_disable",
		},
	})

	return models.InlineKeyboardMarkup{
		InlineKeyboard: buttons,
	}
}

// createExpiryNotificationKeyboard - создает клавиатуру для уведомления в день окончания
func (s *AutoRenewalNotificationService) createExpiryNotificationKeyboard(langCode, paymentMethod string, customerID int64, tariffCode string) models.InlineKeyboardMarkup {
	buttons := [][]models.InlineKeyboardButton{
		{
			{
				Text:         s.translation.GetText(langCode, "manual_renewal_button"),
				CallbackData: "buy",
			},
		},
	}

	// Добавляем кнопку быстрой оплаты для CryptoPay
	if paymentMethod == "cryptopay" {
		quickPaymentButton := []models.InlineKeyboardButton{
			{
				Text:         s.translation.GetText(langCode, "cryptopay_quick_payment_button"),
				CallbackData: fmt.Sprintf("cryptopay_quick_payment:%d:%s", customerID, tariffCode),
			},
		}
		buttons = append(buttons, quickPaymentButton)
	}

	// Добавляем кнопку отключения автопродления
	buttons = append(buttons, []models.InlineKeyboardButton{
		{
			Text:         s.translation.GetText(langCode, "disable_auto_renewal_button"),
			CallbackData: "auto_renewal_disable",
		},
	})

	return models.InlineKeyboardMarkup{
		InlineKeyboard: buttons,
	}
}

// create6hNotificationKeyboard - создает клавиатуру для уведомления за 6 часов
func (s *AutoRenewalNotificationService) create6hNotificationKeyboard(langCode, paymentMethod string, customerID int64, tariffCode string) models.InlineKeyboardMarkup {
	buttons := [][]models.InlineKeyboardButton{
		{
			{
				Text:         s.translation.GetText(langCode, "manual_renewal_button"),
				CallbackData: "buy",
			},
		},
	}

	// Добавляем кнопку быстрой оплаты для CryptoPay
	if paymentMethod == "cryptopay" {
		quickPaymentButton := []models.InlineKeyboardButton{
			{
				Text:         s.translation.GetText(langCode, "cryptopay_quick_payment_button"),
				CallbackData: fmt.Sprintf("cryptopay_quick_payment:%d:%s", customerID, tariffCode),
			},
		}
		buttons = append(buttons, quickPaymentButton)
	}

	// Добавляем кнопку отключения автопродления
	buttons = append(buttons, []models.InlineKeyboardButton{
		{
			Text:         s.translation.GetText(langCode, "disable_auto_renewal_button"),
			CallbackData: "auto_renewal_disable",
		},
	})

	return models.InlineKeyboardMarkup{
		InlineKeyboard: buttons,
	}
}

// create1hNotificationKeyboard - создает клавиатуру для уведомления за 1 час
func (s *AutoRenewalNotificationService) create1hNotificationKeyboard(langCode, paymentMethod string, customerID int64, tariffCode string) models.InlineKeyboardMarkup {
	buttons := [][]models.InlineKeyboardButton{
		{
			{
				Text:         s.translation.GetText(langCode, "manual_renewal_button"),
				CallbackData: "buy",
			},
		},
	}

	// Добавляем кнопку быстрой оплаты для CryptoPay
	if paymentMethod == "cryptopay" {
		quickPaymentButton := []models.InlineKeyboardButton{
			{
				Text:         s.translation.GetText(langCode, "cryptopay_quick_payment_button"),
				CallbackData: fmt.Sprintf("cryptopay_quick_payment:%d:%s", customerID, tariffCode),
			},
		}
		buttons = append(buttons, quickPaymentButton)
	}

	// Добавляем кнопку отключения автопродления
	buttons = append(buttons, []models.InlineKeyboardButton{
		{
			Text:         s.translation.GetText(langCode, "disable_auto_renewal_button"),
			CallbackData: "auto_renewal_disable",
		},
	})

	return models.InlineKeyboardMarkup{
		InlineKeyboard: buttons,
	}
}

// createExpiredNotificationKeyboard - создает клавиатуру для уведомления после истечения
func (s *AutoRenewalNotificationService) createExpiredNotificationKeyboard(langCode, paymentMethod string, customerID int64, tariffCode string) models.InlineKeyboardMarkup {
	buttons := [][]models.InlineKeyboardButton{
		{
			{
				Text:         s.translation.GetText(langCode, "manual_renewal_button"),
				CallbackData: "buy",
			},
		},
	}

	// Добавляем кнопку быстрой оплаты для CryptoPay
	if paymentMethod == "cryptopay" {
		quickPaymentButton := []models.InlineKeyboardButton{
			{
				Text:         s.translation.GetText(langCode, "cryptopay_quick_payment_button"),
				CallbackData: fmt.Sprintf("cryptopay_quick_payment:%d:%s", customerID, tariffCode),
			},
		}
		buttons = append(buttons, quickPaymentButton)
	}

	// Добавляем кнопку отключения автопродления
	buttons = append(buttons, []models.InlineKeyboardButton{
		{
			Text:         s.translation.GetText(langCode, "disable_auto_renewal_button"),
			CallbackData: "auto_renewal_disable",
		},
	})

	return models.InlineKeyboardMarkup{
		InlineKeyboard: buttons,
	}
}
