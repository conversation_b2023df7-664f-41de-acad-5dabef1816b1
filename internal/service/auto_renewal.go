package service

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/interfaces"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// AutoRenewalService - сервис для управления автопродлением подписок
type AutoRenewalService struct {
	autoRenewalRepository            *database.AutoRenewalRepository
	customerRepository               *database.CustomerRepository
	tariffRepository                 *database.TariffRepository
	telegramBot                      *bot.Bot
	paymentInterface                 interfaces.AutoRenewalPaymentInterface
	telegramStarsSubscriptionManager TelegramStarsSubscriptionManager
}

// TelegramStarsSubscriptionManager - интерфейс для управления подписками Telegram Stars
type TelegramStarsSubscriptionManager interface {
	EnableTelegramStarsSubscription(ctx context.Context, customerID int64) error
	DisableTelegramStarsSubscription(ctx context.Context, customerID int64) error
}

// NewAutoRenewalService - создает новый экземпляр сервиса автопродления
func NewAutoRenewalService(
	autoRenewalRepository *database.AutoRenewalRepository,
	customerRepository *database.CustomerRepository,
	tariffRepository *database.TariffRepository,
	telegramBot *bot.Bot,
	paymentInterface interfaces.AutoRenewalPaymentInterface,
) *AutoRenewalService {
	return &AutoRenewalService{
		autoRenewalRepository: autoRenewalRepository,
		customerRepository:    customerRepository,
		tariffRepository:      tariffRepository,
		telegramBot:           telegramBot,
		paymentInterface:      paymentInterface,
	}
}

// SetTelegramStarsSubscriptionManager - устанавливает менеджер подписок Telegram Stars
func (s *AutoRenewalService) SetTelegramStarsSubscriptionManager(manager TelegramStarsSubscriptionManager) {
	s.telegramStarsSubscriptionManager = manager
}

// EnableAutoRenewal - включает автопродление для пользователя
func (s *AutoRenewalService) EnableAutoRenewal(ctx context.Context, customerID int64, paymentMethod string, tariffCode string, savedPaymentData map[string]any) error {
	// Проверяем, что тариф существует
	tariff, err := s.tariffRepository.GetByCode(ctx, tariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}
	if tariff == nil {
		return fmt.Errorf("tariff with code %s not found", tariffCode)
	}

	// Проверяем, что способ оплаты поддерживается
	if !s.isPaymentMethodSupported(paymentMethod) {
		return fmt.Errorf("payment method %s is not supported for auto renewal", paymentMethod)
	}

	// Создаем или обновляем настройки автопродления
	settings := &database.AutoRenewalSettings{
		CustomerID:       customerID,
		Enabled:          true,
		PaymentMethod:    paymentMethod,
		SavedPaymentData: savedPaymentData,
		TariffCode:       tariffCode,
	}

	err = s.autoRenewalRepository.CreateOrUpdateSettings(ctx, settings)
	if err != nil {
		return fmt.Errorf("failed to create or update auto renewal settings: %w", err)
	}

	// Убираем дублирующий лог, так как он уже есть в EnableAutoRenewalAfterPayment
	return nil
}

// DisableAutoRenewal - отключает автопродление для пользователя
func (s *AutoRenewalService) DisableAutoRenewal(ctx context.Context, customerID int64) error {
	err := s.autoRenewalRepository.DisableAutoRenewal(ctx, customerID)
	if err != nil {
		return fmt.Errorf("failed to disable auto renewal: %w", err)
	}

	slog.Info("Auto renewal disabled", "customer_id", customerID)
	return nil
}

// UpdatePaymentMethod - обновляет способ оплаты для автопродления
func (s *AutoRenewalService) UpdatePaymentMethod(ctx context.Context, customerID int64, paymentMethod string) error {
	// Проверяем, что способ оплаты поддерживается
	if !s.isPaymentMethodSupported(paymentMethod) {
		return fmt.Errorf("payment method %s is not supported for auto renewal", paymentMethod)
	}

	// Получаем текущие настройки
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customerID)
	if err != nil {
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}
	if settings == nil {
		return fmt.Errorf("auto renewal settings not found for customer %d", customerID)
	}

	// Обновляем способ оплаты
	settings.PaymentMethod = paymentMethod
	// Очищаем сохраненные данные платежа, так как они относятся к старому способу оплаты
	settings.SavedPaymentData = make(map[string]any)

	err = s.autoRenewalRepository.CreateOrUpdateSettings(ctx, settings)
	if err != nil {
		return fmt.Errorf("failed to update auto renewal settings: %w", err)
	}

	slog.Info("Auto renewal payment method updated", "customer_id", customerID, "new_payment_method", paymentMethod)
	return nil
}

// GetAutoRenewalSettings - получает настройки автопродления для пользователя
func (s *AutoRenewalService) GetAutoRenewalSettings(ctx context.Context, customerID int64) (*database.AutoRenewalSettings, error) {
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get auto renewal settings: %w", err)
	}
	return settings, nil
}

// IsAutoRenewalEnabled - проверяет, включено ли автопродление для пользователя
func (s *AutoRenewalService) IsAutoRenewalEnabled(ctx context.Context, customerID int64) (bool, error) {
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customerID)
	if err != nil {
		return false, fmt.Errorf("failed to get auto renewal settings: %w", err)
	}
	if settings == nil {
		return false, nil
	}
	return settings.Enabled, nil
}

// ProcessAutoRenewals - обрабатывает автопродления для пользователей с истекающими подписками
func (s *AutoRenewalService) ProcessAutoRenewals(ctx context.Context) error {
	// Получаем настройки автопродления для пользователей с подписками, истекающими в течение часа
	settings, err := s.autoRenewalRepository.GetEnabledSettingsForRenewal(ctx, time.Now().Add(time.Hour))
	if err != nil {
		return fmt.Errorf("failed to get enabled settings for renewal: %w", err)
	}

	slog.Info("Processing auto renewals", "count", len(settings))

	for _, setting := range settings {
		err := s.processAutoRenewalForCustomer(ctx, setting)
		if err != nil {
			slog.Error("Failed to process auto renewal for customer",
				"customer_id", setting.CustomerID,
				"error", err)
			continue
		}
	}

	return nil
}

// processAutoRenewalForCustomer - обрабатывает автопродление для конкретного пользователя
func (s *AutoRenewalService) processAutoRenewalForCustomer(ctx context.Context, settings database.AutoRenewalSettings) error {
	// Получаем информацию о пользователе
	customer, err := s.customerRepository.FindById(ctx, settings.CustomerID)
	if err != nil {
		return fmt.Errorf("failed to get customer: %w", err)
	}
	if customer == nil {
		return fmt.Errorf("customer not found")
	}

	// Получаем информацию о тарифе
	tariff, err := s.tariffRepository.GetByCode(ctx, settings.TariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}
	if tariff == nil {
		return fmt.Errorf("tariff not found")
	}

	// Создаем запись в истории (статус pending)
	historyRecord := &database.AutoRenewalHistory{
		CustomerID:            settings.CustomerID,
		AutoRenewalSettingsID: settings.ID,
		Status:                database.AutoRenewalHistoryStatusPending,
		PaymentMethod:         &settings.PaymentMethod,
		TariffCode:            &settings.TariffCode,
	}

	err = s.autoRenewalRepository.CreateHistoryRecord(ctx, historyRecord)
	if err != nil {
		return fmt.Errorf("failed to create history record: %w", err)
	}

	// Проверяем поддерживаемость способа оплаты
	if !s.isPaymentMethodSupported(settings.PaymentMethod) {
		errorMsg := fmt.Sprintf("unsupported payment method: %s", settings.PaymentMethod)
		s.updateHistoryRecordWithError(ctx, historyRecord.ID, errorMsg)
		return fmt.Errorf("unsupported payment method: %s", settings.PaymentMethod)
	}

	// Пытаемся создать автоматический платеж
	err = s.createAutoRenewalPayment(ctx, customer, tariff, settings, historyRecord.ID)
	if err != nil {
		errorMsg := fmt.Sprintf("failed to create auto renewal payment: %v", err)
		s.updateHistoryRecordWithError(ctx, historyRecord.ID, errorMsg)

		// Отправляем уведомление о неудачном автопродлении
		s.sendAutoRenewalFailedNotification(ctx, customer, tariff, err.Error())

		return fmt.Errorf("failed to create auto renewal payment: %w", err)
	}

	slog.Info("Auto renewal processed successfully",
		"customer_id", settings.CustomerID,
		"tariff_code", settings.TariffCode,
		"payment_method", settings.PaymentMethod)

	return nil
}

// createAutoRenewalPayment - создает автоматический платеж для продления подписки
func (s *AutoRenewalService) createAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff, settings database.AutoRenewalSettings, historyID int64) error {
	// Для Telegram Stars используем специальную логику управления подписками
	if settings.PaymentMethod == "telegram" {
		return s.processTelegramStarsAutoRenewal(ctx, customer, tariff, settings, historyID)
	}

	// Для остальных способов оплаты используем интерфейс для создания автоматического платежа
	purchaseID, err := s.paymentInterface.CreateAutoRenewalPayment(ctx, customer, tariff, settings.PaymentMethod, settings.SavedPaymentData)
	if err != nil {
		return fmt.Errorf("failed to create auto renewal payment: %w", err)
	}

	// Обновляем запись в истории с успешным результатом
	s.updateHistoryRecordWithSuccess(ctx, historyID, purchaseID, float64(tariff.PriceRUB), "RUB")

	return nil
}

// processTelegramStarsAutoRenewal - обрабатывает автопродление для подписок Telegram Stars
func (s *AutoRenewalService) processTelegramStarsAutoRenewal(ctx context.Context, customer *database.Customer, tariff *database.Tariff, settings database.AutoRenewalSettings, historyID int64) error {
	// Проверяем статус подписки через сохраненные данные
	subscriptionExpirationDate, hasExpiration := settings.SavedPaymentData["subscription_expiration_date"]
	isRecurring, hasRecurring := settings.SavedPaymentData["is_recurring"].(bool)

	// Если подписка рекуррентная и не истекла, просто продлеваем доступ пользователю
	if hasRecurring && isRecurring && hasExpiration {
		if expirationTimestamp, ok := subscriptionExpirationDate.(float64); ok {
			expirationTime := time.Unix(int64(expirationTimestamp), 0)

			// Если подписка еще активна, продлеваем доступ пользователю
			if expirationTime.After(time.Now()) {
				return s.extendCustomerSubscription(ctx, customer, tariff, settings, historyID)
			}
		}
	}

	// Если подписка неактивна или истекла, пытаемся её возобновить
	if s.telegramStarsSubscriptionManager != nil {
		err := s.telegramStarsSubscriptionManager.EnableTelegramStarsSubscription(ctx, customer.ID)
		if err != nil {
			slog.Error("Failed to enable Telegram Stars subscription for auto renewal",
				"customer_id", customer.ID, "error", err)
			return fmt.Errorf("failed to enable Telegram Stars subscription: %w", err)
		}

		// После успешного возобновления подписки продлеваем доступ
		return s.extendCustomerSubscription(ctx, customer, tariff, settings, historyID)
	}

	return fmt.Errorf("telegram stars subscription manager not available")
}

// extendCustomerSubscription - продлевает подписку пользователя без создания нового платежа
func (s *AutoRenewalService) extendCustomerSubscription(ctx context.Context, customer *database.Customer, tariff *database.Tariff, settings database.AutoRenewalSettings, historyID int64) error {
	// Рассчитываем новую дату окончания подписки на основе количества месяцев в тарифе
	months := tariff.GetMonthsFromCode()
	durationDays := months * 30 // Приблизительно 30 дней в месяце

	var newExpireAt time.Time
	if customer.ExpireAt != nil {
		newExpireAt = customer.ExpireAt.Add(time.Duration(durationDays) * 24 * time.Hour)
	} else {
		// Если у пользователя нет активной подписки, начинаем с текущего времени
		newExpireAt = time.Now().Add(time.Duration(durationDays) * 24 * time.Hour)
	}

	// Обновляем дату окончания подписки у пользователя
	updates := map[string]interface{}{
		"expire_at": newExpireAt,
	}
	err := s.customerRepository.UpdateFields(ctx, customer.ID, updates)
	if err != nil {
		return fmt.Errorf("failed to update customer expire_at: %w", err)
	}

	// Обновляем запись в истории как успешную (без purchase_id, так как это продление подписки)
	err = s.autoRenewalRepository.UpdateHistoryRecordWithSuccess(ctx, historyID, 0, float64(tariff.PriceStars), "XTR")
	if err != nil {
		slog.Error("Failed to update history record after subscription extension",
			"history_id", historyID, "customer_id", customer.ID, "error", err)
		// Не возвращаем ошибку, так как подписка уже продлена
	}

	slog.Info("Telegram Stars subscription extended successfully",
		"customer_id", customer.ID,
		"new_expire_at", newExpireAt,
		"tariff_code", tariff.Code,
		"months", months)

	// Отправляем уведомление пользователю об успешном продлении
	s.sendTelegramStarsRenewalSuccessNotification(ctx, customer, tariff, newExpireAt)

	return nil
}

// isPaymentMethodSupported - проверяет, поддерживается ли способ оплаты для автопродления
func (s *AutoRenewalService) isPaymentMethodSupported(paymentMethod string) bool {
	supportedMethods := []string{"yookasa", "cryptopay", "telegram", "tribute"}
	for _, method := range supportedMethods {
		if method == paymentMethod {
			return true
		}
	}
	return false
}

// EnableAutoRenewalAfterPayment - включает автопродление после успешной оплаты
func (s *AutoRenewalService) EnableAutoRenewalAfterPayment(ctx context.Context, customerID int64, paymentMethod string, tariffCode string, paymentMethodData map[string]any) error {
	// Проверяем, поддерживается ли способ оплаты для автопродления
	if !s.isPaymentMethodSupported(paymentMethod) {
		slog.Warn("Payment method not supported for auto renewal",
			"payment_method", paymentMethod,
			"customer_id", customerID)
		return nil // Не возвращаем ошибку, просто не включаем автопродление
	}

	// Проверяем, существует ли тариф
	tariff, err := s.tariffRepository.GetByCode(ctx, tariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}
	if tariff == nil {
		return fmt.Errorf("tariff not found: %s", tariffCode)
	}

	// Включаем автопродление
	err = s.EnableAutoRenewal(ctx, customerID, paymentMethod, tariffCode, paymentMethodData)
	if err != nil {
		return fmt.Errorf("failed to enable auto renewal: %w", err)
	}

	// Лог уже выводится в payment.go, убираем дублирование

	return nil
}

// updateHistoryRecordWithError - обновляет запись в истории с ошибкой
func (s *AutoRenewalService) updateHistoryRecordWithError(ctx context.Context, historyID int64, errorMessage string) {
	err := s.autoRenewalRepository.UpdateHistoryRecordWithError(ctx, historyID, errorMessage)
	if err != nil {
		slog.Error("Failed to update history record with error", "history_id", historyID, "error", err)
	}
}

// updateHistoryRecordWithSuccess - обновляет запись в истории при успешном автопродлении
func (s *AutoRenewalService) updateHistoryRecordWithSuccess(ctx context.Context, historyID int64, purchaseID int64, amount float64, currency string) {
	err := s.autoRenewalRepository.UpdateHistoryRecordWithSuccess(ctx, historyID, purchaseID, amount, currency)
	if err != nil {
		slog.Error("Failed to update history record with success", "history_id", historyID, "error", err)
	}
}

// sendAutoRenewalFailedNotification - отправляет уведомление о неудачном автопродлении
func (s *AutoRenewalService) sendAutoRenewalFailedNotification(ctx context.Context, customer *database.Customer, tariff *database.Tariff, errorMessage string) {
	// Получаем язык пользователя
	langCode := customer.Language
	if langCode == "" {
		langCode = "ru" // Язык по умолчанию
	}

	// Получаем настройки автопродления для определения способа оплаты
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customer.ID)
	if err != nil {
		slog.Error("Failed to get auto renewal settings for failed notification", "error", err, "customer_id", customer.ID)
		// Используем общее уведомление
		settings = &database.AutoRenewalSettings{PaymentMethod: "unknown"}
	}

	// Формируем текст уведомления в зависимости от способа оплаты
	var messageText string
	if settings.PaymentMethod == "cryptopay" {
		// Специальное уведомление для CryptoPay (используем хардкод, так как нет translation в этом сервисе)
		if langCode == "en" {
			messageText = fmt.Sprintf(
				"❌ <b>CryptoPay Auto-renewal Error</b>\n\n"+
					"🚫 <b>Failed to renew subscription automatically</b>\n\n"+
					"📦 Plan: <b>%s</b>\n"+
					"💰 Amount: <b>%d₽</b>\n"+
					"🪙 Payment method: <b>CryptoPay</b>\n"+
					"⏰ Expired: <b>%s</b>\n\n"+
					"🔍 <b>Possible reasons:</b>\n"+
					"• Insufficient balance\n"+
					"• CryptoPay technical issues\n"+
					"• Invoice expired\n"+
					"• Network problems\n\n"+
					"💡 <b>What to do:</b>\n"+
					"• Check your USDT balance\n"+
					"• Renew subscription manually\n"+
					"• Set up auto-renewal again\n\n"+
					"⚙️ Auto-renewal was automatically disabled to prevent repeated errors.",
				tariff.Title,
				tariff.PriceRUB,
				customer.ExpireAt.Format("02.01.2006 15:04"),
			)
		} else {
			messageText = fmt.Sprintf(
				"❌ <b>Ошибка автопродления CryptoPay</b>\n\n"+
					"🚫 <b>Не удалось продлить подписку автоматически</b>\n\n"+
					"📦 Тариф: <b>%s</b>\n"+
					"💰 Сумма: <b>%d₽</b>\n"+
					"🪙 Способ оплаты: <b>CryptoPay</b>\n"+
					"⏰ Истекла: <b>%s</b>\n\n"+
					"🔍 <b>Возможные причины:</b>\n"+
					"• Недостаточно средств на балансе\n"+
					"• Технические проблемы CryptoPay\n"+
					"• Истек срок действия инвойса\n"+
					"• Сетевые проблемы\n\n"+
					"💡 <b>Что делать:</b>\n"+
					"• Проверьте баланс USDT\n"+
					"• Продлите подписку вручную\n"+
					"• Настройте автопродление заново\n\n"+
					"⚙️ Автопродление было автоматически отключено для предотвращения повторных ошибок.",
				tariff.Title,
				tariff.PriceRUB,
				customer.ExpireAt.Format("02.01.2006 15:04"),
			)
		}
	} else {
		// Стандартное уведомление для других способов оплаты
		messageText = fmt.Sprintf(
			"❌ <b>Ошибка автопродления</b>\n\n"+
				"📦 Тариф: <b>%s</b>\n"+
				"⏰ Дата окончания: <b>%s</b>\n\n"+
				"🔴 Причина: %s\n\n"+
				"Для продления подписки воспользуйтесь ручным способом оплаты.",
			tariff.Title,
			customer.ExpireAt.Format("02.01.2006 15:04"),
			errorMessage,
		)
	}

	// Создаем клавиатуру с кнопками действий
	keyboard := models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text:         "🛒 Продлить вручную",
					CallbackData: "buy",
				},
			},
			{
				{
					Text:         "⚙️ Настроить автопродление",
					CallbackData: "auto_renewal_toggle",
				},
			},
		},
	}

	// Отправляем уведомление
	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Failed to send auto renewal failed notification",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID,
			"error", err)
	} else {
		slog.Info("Auto renewal failed notification sent successfully",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID)
	}
}

// sendTelegramStarsRenewalSuccessNotification - отправляет уведомление об успешном продлении подписки Telegram Stars
func (s *AutoRenewalService) sendTelegramStarsRenewalSuccessNotification(ctx context.Context, customer *database.Customer, tariff *database.Tariff, newExpireAt time.Time) {
	// Получаем язык пользователя
	langCode := customer.Language
	if langCode == "" {
		langCode = "ru" // Язык по умолчанию
	}

	// Формируем текст уведомления
	messageText := fmt.Sprintf(
		"✅ <b>Подписка автоматически продлена</b>\n\n"+
			"⭐ <b>Telegram Stars</b>\n"+
			"📦 Тариф: <b>%s</b>\n"+
			"💰 Стоимость: <b>%d ⭐</b>\n"+
			"⏰ Действует до: <b>%s</b>\n\n"+
			"🔄 Автопродление активно\n"+
			"Следующее списание произойдет автоматически.",
		tariff.Title,
		tariff.PriceStars,
		newExpireAt.Format("02.01.2006 15:04"),
	)

	// Создаем клавиатуру с кнопками действий
	keyboard := models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text:         "📱 Главное меню",
					CallbackData: "start",
				},
			},
			{
				{
					Text:         "⚙️ Настройки автопродления",
					CallbackData: "auto_renewal_toggle",
				},
			},
		},
	}

	// Отправляем уведомление
	_, err := s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Failed to send Telegram Stars renewal success notification",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID,
			"error", err)
	} else {
		slog.Info("Telegram Stars renewal success notification sent successfully",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID,
			"new_expire_at", newExpireAt)
	}
}

// GetAutoRenewalHistory - получает историю автопродлений для пользователя
func (s *AutoRenewalService) GetAutoRenewalHistory(ctx context.Context, customerID int64, limit int) ([]database.AutoRenewalHistory, error) {
	history, err := s.autoRenewalRepository.GetHistoryByCustomerID(ctx, customerID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get auto renewal history: %w", err)
	}
	return history, nil
}

// DeleteAutoRenewalSettings - удаляет настройки автопродления и все сохраненные платежные данные
func (s *AutoRenewalService) DeleteAutoRenewalSettings(ctx context.Context, customerID int64) error {
	err := s.autoRenewalRepository.DeleteSettings(ctx, customerID)
	if err != nil {
		return fmt.Errorf("failed to delete auto renewal settings: %w", err)
	}

	slog.Info("Auto renewal settings deleted", "customer_id", customerID)
	return nil
}

// EnableTelegramStarsSubscription - включает автопродление подписки Telegram Stars
func (s *AutoRenewalService) EnableTelegramStarsSubscription(ctx context.Context, customerID int64) error {
	if s.telegramStarsSubscriptionManager == nil {
		return fmt.Errorf("telegram stars subscription manager not set")
	}

	// Проверяем, что у пользователя есть настройки автопродления с Telegram Stars
	settings, err := s.GetAutoRenewalSettings(ctx, customerID)
	if err != nil {
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	if settings == nil {
		return fmt.Errorf("auto renewal settings not found for customer %d", customerID)
	}

	if settings.PaymentMethod != "telegram" {
		return fmt.Errorf("customer %d does not have Telegram Stars payment method configured", customerID)
	}

	// Вызываем метод включения подписки
	err = s.telegramStarsSubscriptionManager.EnableTelegramStarsSubscription(ctx, customerID)
	if err != nil {
		return fmt.Errorf("failed to enable Telegram Stars subscription: %w", err)
	}

	// Обновляем статус в базе данных
	err = s.autoRenewalRepository.UpdateStatus(ctx, customerID, true)
	if err != nil {
		slog.Error("Failed to update auto renewal status after enabling subscription", "error", err, "customer_id", customerID)
		// Не возвращаем ошибку, так как подписка уже включена в Telegram
	}

	slog.Info("Telegram Stars subscription enabled successfully", "customer_id", customerID)
	return nil
}

// DisableTelegramStarsSubscription - отключает автопродление подписки Telegram Stars
func (s *AutoRenewalService) DisableTelegramStarsSubscription(ctx context.Context, customerID int64) error {
	if s.telegramStarsSubscriptionManager == nil {
		return fmt.Errorf("telegram stars subscription manager not set")
	}

	// Проверяем, что у пользователя есть настройки автопродления с Telegram Stars
	settings, err := s.GetAutoRenewalSettings(ctx, customerID)
	if err != nil {
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	if settings == nil {
		return fmt.Errorf("auto renewal settings not found for customer %d", customerID)
	}

	if settings.PaymentMethod != "telegram" {
		return fmt.Errorf("customer %d does not have Telegram Stars payment method configured", customerID)
	}

	// Вызываем метод отключения подписки
	err = s.telegramStarsSubscriptionManager.DisableTelegramStarsSubscription(ctx, customerID)
	if err != nil {
		return fmt.Errorf("failed to disable Telegram Stars subscription: %w", err)
	}

	// Обновляем статус в базе данных
	err = s.autoRenewalRepository.UpdateStatus(ctx, customerID, false)
	if err != nil {
		slog.Error("Failed to update auto renewal status after disabling subscription", "error", err, "customer_id", customerID)
		// Не возвращаем ошибку, так как подписка уже отключена в Telegram
	}

	slog.Info("Telegram Stars subscription disabled successfully", "customer_id", customerID)
	return nil
}
