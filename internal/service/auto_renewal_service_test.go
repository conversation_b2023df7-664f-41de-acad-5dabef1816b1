package service

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"remnawave-tg-shop-bot/internal/database"
)

// MockAutoRenewalRepository - мок для репозитория автопродления
type MockAutoRenewalRepository struct {
	mock.Mock
}

func (m *MockAutoRenewalRepository) GetByCustomerID(ctx context.Context, customerID int64) (*database.AutoRenewalSettings, error) {
	args := m.Called(ctx, customerID)
	return args.Get(0).(*database.AutoRenewalSettings), args.Error(1)
}

func (m *MockAutoRenewalRepository) Create(ctx context.Context, settings *database.AutoRenewalSettings) error {
	args := m.Called(ctx, settings)
	return args.Error(0)
}

func (m *MockAutoRenewalRepository) Update(ctx context.Context, settings *database.AutoRenewalSettings) error {
	args := m.Called(ctx, settings)
	return args.Error(0)
}

func (m *MockAutoRenewalRepository) UpdateSavedPaymentData(ctx context.Context, customerID int64, paymentData map[string]any) error {
	args := m.Called(ctx, customerID, paymentData)
	return args.Error(0)
}

func (m *MockAutoRenewalRepository) GetActiveAutoRenewals(ctx context.Context) ([]*database.AutoRenewalSettings, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*database.AutoRenewalSettings), args.Error(1)
}

// MockPaymentInterface - мок для интерфейса платежей
type MockPaymentInterface struct {
	mock.Mock
}

func (m *MockPaymentInterface) CreateAutoRenewalInvoice(ctx context.Context, customer *database.Customer, tariff *database.Tariff, invoiceType database.InvoiceType, savedPaymentData map[string]any) (string, int64, error) {
	args := m.Called(ctx, customer, tariff, invoiceType, savedPaymentData)
	return args.String(0), args.Get(1).(int64), args.Error(2)
}

// MockCustomerRepository - мок для репозитория клиентов
type MockCustomerRepository struct {
	mock.Mock
}

func (m *MockCustomerRepository) FindByID(ctx context.Context, id int64) (*database.Customer, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*database.Customer), args.Error(1)
}

// MockTariffRepository - мок для репозитория тарифов
type MockTariffRepository struct {
	mock.Mock
}

func (m *MockTariffRepository) GetByCode(ctx context.Context, code string) (*database.Tariff, error) {
	args := m.Called(ctx, code)
	return args.Get(0).(*database.Tariff), args.Error(1)
}

// Тестовые данные
func createTestAutoRenewalSettings(enabled bool, invoiceType database.InvoiceType) *database.AutoRenewalSettings {
	savedPaymentData := map[string]any{}
	if enabled && invoiceType == database.InvoiceTypeYookasa {
		savedPaymentData = map[string]any{
			"payment_method_id": "pm_test_123456",
			"card_mask":         "**** 1234",
			"card_type":         "MasterCard",
		}
	}

	savedPaymentDataJSON, _ := json.Marshal(savedPaymentData)

	return &database.AutoRenewalSettings{
		ID:               1,
		CustomerID:       123,
		Enabled:          enabled,
		InvoiceType:      invoiceType,
		SavedPaymentData: savedPaymentDataJSON,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}
}

func createTestCustomerForService() *database.Customer {
	return &database.Customer{
		ID:         123,
		TelegramID: 456789,
		Language:   "ru",
		TariffCode: "1month",
	}
}

func createTestTariffForService() *database.Tariff {
	return &database.Tariff{
		Code:     "1month",
		PriceRUB: 500,
	}
}

// TestGetAutoRenewalSettings - тест получения настроек автопродления
func TestGetAutoRenewalSettings(t *testing.T) {
	tests := []struct {
		name          string
		customerID    int64
		setupMocks    func(*MockAutoRenewalRepository)
		expectedError bool
	}{
		{
			name:       "Успешное получение настроек",
			customerID: 123,
			setupMocks: func(repo *MockAutoRenewalRepository) {
				settings := createTestAutoRenewalSettings(true, database.InvoiceTypeYookasa)
				repo.On("GetByCustomerID", mock.Anything, int64(123)).Return(settings, nil)
			},
			expectedError: false,
		},
		{
			name:       "Настройки не найдены",
			customerID: 999,
			setupMocks: func(repo *MockAutoRenewalRepository) {
				repo.On("GetByCustomerID", mock.Anything, int64(999)).Return((*database.AutoRenewalSettings)(nil), assert.AnError)
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Создаем мок
			mockRepo := new(MockAutoRenewalRepository)

			// Настраиваем мок
			tt.setupMocks(mockRepo)

			// Создаем сервис
			service := &AutoRenewalService{
				autoRenewalRepository: mockRepo,
			}

			// Выполняем тест
			settings, err := service.GetAutoRenewalSettings(context.Background(), tt.customerID)

			// Проверяем результат
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, settings)
				assert.Equal(t, tt.customerID, settings.CustomerID)
			}

			// Проверяем, что все ожидаемые вызовы были сделаны
			mockRepo.AssertExpectations(t)
		})
	}
}

// TestToggleAutoRenewal - тест переключения автопродления
func TestToggleAutoRenewal(t *testing.T) {
	tests := []struct {
		name          string
		customerID    int64
		setupMocks    func(*MockAutoRenewalRepository)
		expectedError bool
	}{
		{
			name:       "Отключение автопродления",
			customerID: 123,
			setupMocks: func(repo *MockAutoRenewalRepository) {
				// Существующие настройки (включено)
				settings := createTestAutoRenewalSettings(true, database.InvoiceTypeYookasa)
				repo.On("GetByCustomerID", mock.Anything, int64(123)).Return(settings, nil)

				// Обновление настроек (отключение)
				repo.On("Update", mock.Anything, mock.MatchedBy(func(s *database.AutoRenewalSettings) bool {
					return s.CustomerID == 123 && !s.Enabled
				})).Return(nil)
			},
			expectedError: false,
		},
		{
			name:       "Включение автопродления",
			customerID: 123,
			setupMocks: func(repo *MockAutoRenewalRepository) {
				// Существующие настройки (отключено)
				settings := createTestAutoRenewalSettings(false, database.InvoiceTypeYookasa)
				repo.On("GetByCustomerID", mock.Anything, int64(123)).Return(settings, nil)

				// Обновление настроек (включение)
				repo.On("Update", mock.Anything, mock.MatchedBy(func(s *database.AutoRenewalSettings) bool {
					return s.CustomerID == 123 && s.Enabled
				})).Return(nil)
			},
			expectedError: false,
		},
		{
			name:       "Создание новых настроек при их отсутствии",
			customerID: 456,
			setupMocks: func(repo *MockAutoRenewalRepository) {
				// Настройки не найдены
				repo.On("GetByCustomerID", mock.Anything, int64(456)).Return((*database.AutoRenewalSettings)(nil), assert.AnError)

				// Создание новых настроек
				repo.On("Create", mock.Anything, mock.MatchedBy(func(s *database.AutoRenewalSettings) bool {
					return s.CustomerID == 456 && s.Enabled
				})).Return(nil)
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Создаем мок
			mockRepo := new(MockAutoRenewalRepository)

			// Настраиваем мок
			tt.setupMocks(mockRepo)

			// Создаем сервис
			service := &AutoRenewalService{
				autoRenewalRepository: mockRepo,
			}

			// Выполняем тест
			err := service.ToggleAutoRenewal(context.Background(), tt.customerID)

			// Проверяем результат
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Проверяем, что все ожидаемые вызовы были сделаны
			mockRepo.AssertExpectations(t)
		})
	}
}

// TestSetupAutoRenewal - тест настройки автопродления
func TestSetupAutoRenewal(t *testing.T) {
	tests := []struct {
		name          string
		customerID    int64
		invoiceType   database.InvoiceType
		setupMocks    func(*MockAutoRenewalRepository)
		expectedError bool
	}{
		{
			name:        "Настройка автопродления YooKassa",
			customerID:  123,
			invoiceType: database.InvoiceTypeYookasa,
			setupMocks: func(repo *MockAutoRenewalRepository) {
				// Существующие настройки
				settings := createTestAutoRenewalSettings(false, database.InvoiceTypeYookasa)
				repo.On("GetByCustomerID", mock.Anything, int64(123)).Return(settings, nil)

				// Обновление настроек
				repo.On("Update", mock.Anything, mock.MatchedBy(func(s *database.AutoRenewalSettings) bool {
					return s.CustomerID == 123 && s.Enabled && s.InvoiceType == database.InvoiceTypeYookasa
				})).Return(nil)
			},
			expectedError: false,
		},
		{
			name:        "Создание новых настроек для Telegram Stars",
			customerID:  456,
			invoiceType: database.InvoiceTypeTelegram,
			setupMocks: func(repo *MockAutoRenewalRepository) {
				// Настройки не найдены
				repo.On("GetByCustomerID", mock.Anything, int64(456)).Return((*database.AutoRenewalSettings)(nil), assert.AnError)

				// Создание новых настроек
				repo.On("Create", mock.Anything, mock.MatchedBy(func(s *database.AutoRenewalSettings) bool {
					return s.CustomerID == 456 && s.Enabled && s.InvoiceType == database.InvoiceTypeTelegram
				})).Return(nil)
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Создаем мок
			mockRepo := new(MockAutoRenewalRepository)

			// Настраиваем мок
			tt.setupMocks(mockRepo)

			// Создаем сервис
			service := &AutoRenewalService{
				autoRenewalRepository: mockRepo,
			}

			// Выполняем тест
			err := service.SetupAutoRenewal(context.Background(), tt.customerID, tt.invoiceType)

			// Проверяем результат
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Проверяем, что все ожидаемые вызовы были сделаны
			mockRepo.AssertExpectations(t)
		})
	}
}
