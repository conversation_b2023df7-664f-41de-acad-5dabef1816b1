package database

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	sq "github.com/Masterminds/squirrel"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
)

// AutoRenewalRepository - репозиторий для работы с настройками автопродления
type AutoRenewalRepository struct {
	pool *pgxpool.Pool
}

func NewAutoRenewalRepository(pool *pgxpool.Pool) *AutoRenewalRepository {
	return &AutoRenewalRepository{pool: pool}
}

// AutoRenewalSettings - модель настроек автопродления
type AutoRenewalSettings struct {
	ID               int64          `db:"id"`
	CustomerID       int64          `db:"customer_id"`
	Enabled          bool           `db:"enabled"`
	PaymentMethod    string         `db:"payment_method"`
	SavedPaymentData map[string]any `db:"saved_payment_data"`
	TariffCode       string         `db:"tariff_code"`
	CreatedAt        time.Time      `db:"created_at"`
	UpdatedAt        time.Time      `db:"updated_at"`
}

// AutoRenewalHistoryStatus - статус операции автопродления
type AutoRenewalHistoryStatus string

const (
	AutoRenewalHistoryStatusSuccess   AutoRenewalHistoryStatus = "success"
	AutoRenewalHistoryStatusFailed    AutoRenewalHistoryStatus = "failed"
	AutoRenewalHistoryStatusCancelled AutoRenewalHistoryStatus = "cancelled"
	AutoRenewalHistoryStatusPending   AutoRenewalHistoryStatus = "pending"
)

// AutoRenewalHistory - модель истории автопродлений
type AutoRenewalHistory struct {
	ID                    int64                    `db:"id"`
	CustomerID            int64                    `db:"customer_id"`
	PurchaseID            *int64                   `db:"purchase_id"`
	AutoRenewalSettingsID int64                    `db:"auto_renewal_settings_id"`
	Status                AutoRenewalHistoryStatus `db:"status"`
	ErrorMessage          *string                  `db:"error_message"`
	Amount                *float64                 `db:"amount"`
	Currency              *string                  `db:"currency"`
	PaymentMethod         *string                  `db:"payment_method"`
	TariffCode            *string                  `db:"tariff_code"`
	ProcessedAt           time.Time                `db:"processed_at"`
}

// AutoRenewalNotificationType - тип уведомления об автопродлении
type AutoRenewalNotificationType string

const (
	AutoRenewalNotification3d      AutoRenewalNotificationType = "3d"      // За 3 дня до окончания (одно уведомление)
	AutoRenewalNotification1d      AutoRenewalNotificationType = "1d"      // За 1 день до окончания (одно уведомление)
	AutoRenewalNotification6h      AutoRenewalNotificationType = "6h"      // За 6 часов до окончания (одно уведомление)
	AutoRenewalNotification1h      AutoRenewalNotificationType = "1h"      // За 1 час до окончания (одно уведомление)
	AutoRenewalNotificationExpired AutoRenewalNotificationType = "expired" // После истечения подписки (одно уведомление)
	AutoRenewalNotificationFailed  AutoRenewalNotificationType = "failed"
	AutoRenewalNotificationSuccess AutoRenewalNotificationType = "success"
)

// AutoRenewalNotification - модель уведомления об автопродлении
type AutoRenewalNotification struct {
	ID                    int64                       `db:"id"`
	CustomerID            int64                       `db:"customer_id"`
	NotificationType      AutoRenewalNotificationType `db:"notification_type"`
	SentAt                time.Time                   `db:"sent_at"`
	SubscriptionExpiresAt time.Time                   `db:"subscription_expires_at"`
}

// CreateOrUpdateSettings - создает или обновляет настройки автопродления
func (r *AutoRenewalRepository) CreateOrUpdateSettings(ctx context.Context, settings *AutoRenewalSettings) error {
	// Сериализуем JSON данные
	paymentDataJSON, err := json.Marshal(settings.SavedPaymentData)
	if err != nil {
		return fmt.Errorf("failed to marshal payment data: %w", err)
	}

	// Проверяем, существуют ли настройки для данного пользователя
	existing, err := r.GetSettingsByCustomerID(ctx, settings.CustomerID)
	if err != nil && err != pgx.ErrNoRows {
		return fmt.Errorf("failed to check existing settings: %w", err)
	}

	if existing != nil {
		// Обновляем существующие настройки
		buildUpdate := sq.Update("auto_renewal_settings").
			Set("enabled", settings.Enabled).
			Set("payment_method", settings.PaymentMethod).
			Set("saved_payment_data", paymentDataJSON).
			Set("tariff_code", settings.TariffCode).
			Where(sq.Eq{"customer_id": settings.CustomerID}).
			PlaceholderFormat(sq.Dollar)

		sql, args, err := buildUpdate.ToSql()
		if err != nil {
			return fmt.Errorf("failed to build update query: %w", err)
		}

		_, err = r.pool.Exec(ctx, sql, args...)
		if err != nil {
			return fmt.Errorf("failed to update auto renewal settings: %w", err)
		}

		settings.ID = existing.ID
	} else {
		// Создаем новые настройки
		buildInsert := sq.Insert("auto_renewal_settings").
			Columns("customer_id", "enabled", "payment_method", "saved_payment_data", "tariff_code").
			Values(settings.CustomerID, settings.Enabled, settings.PaymentMethod, paymentDataJSON, settings.TariffCode).
			Suffix("RETURNING id, created_at, updated_at").
			PlaceholderFormat(sq.Dollar)

		sql, args, err := buildInsert.ToSql()
		if err != nil {
			return fmt.Errorf("failed to build insert query: %w", err)
		}

		err = r.pool.QueryRow(ctx, sql, args...).Scan(&settings.ID, &settings.CreatedAt, &settings.UpdatedAt)
		if err != nil {
			return fmt.Errorf("failed to create auto renewal settings: %w", err)
		}
	}

	return nil
}

// UpdateSavedPaymentData - обновляет только сохраненные данные платежного метода
func (r *AutoRenewalRepository) UpdateSavedPaymentData(ctx context.Context, customerID int64, savedPaymentData map[string]any) error {
	// Сериализуем JSON данные
	paymentDataJSON, err := json.Marshal(savedPaymentData)
	if err != nil {
		return fmt.Errorf("failed to marshal payment data: %w", err)
	}

	buildUpdate := sq.Update("auto_renewal_settings").
		Set("saved_payment_data", paymentDataJSON).
		Where(sq.Eq{"customer_id": customerID}).
		PlaceholderFormat(sq.Dollar)

	sql, args, err := buildUpdate.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build update query: %w", err)
	}

	_, err = r.pool.Exec(ctx, sql, args...)
	if err != nil {
		return fmt.Errorf("failed to update saved payment data: %w", err)
	}

	return nil
}

// UpdateStatus - обновляет статус автопродления для пользователя
func (r *AutoRenewalRepository) UpdateStatus(ctx context.Context, customerID int64, enabled bool) error {
	buildUpdate := sq.Update("auto_renewal_settings").
		Set("enabled", enabled).
		Set("updated_at", sq.Expr("NOW()")).
		Where(sq.Eq{"customer_id": customerID}).
		PlaceholderFormat(sq.Dollar)

	sqlStr, args, err := buildUpdate.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build update query: %w", err)
	}

	_, err = r.pool.Exec(ctx, sqlStr, args...)
	if err != nil {
		return fmt.Errorf("failed to update auto renewal status: %w", err)
	}

	return nil
}

// GetByCustomerID - алиас для GetSettingsByCustomerID для совместимости
func (r *AutoRenewalRepository) GetByCustomerID(ctx context.Context, customerID int64) (*AutoRenewalSettings, error) {
	return r.GetSettingsByCustomerID(ctx, customerID)
}

// GetSettingsByCustomerID - получает настройки автопродления по ID пользователя
func (r *AutoRenewalRepository) GetSettingsByCustomerID(ctx context.Context, customerID int64) (*AutoRenewalSettings, error) {
	buildSelect := sq.Select("id", "customer_id", "enabled", "payment_method", "saved_payment_data", "tariff_code", "created_at", "updated_at").
		From("auto_renewal_settings").
		Where(sq.Eq{"customer_id": customerID}).
		PlaceholderFormat(sq.Dollar)

	sql, args, err := buildSelect.ToSql()
	if err != nil {
		return nil, fmt.Errorf("failed to build select query: %w", err)
	}

	var settings AutoRenewalSettings
	var paymentDataJSON []byte

	err = r.pool.QueryRow(ctx, sql, args...).Scan(
		&settings.ID,
		&settings.CustomerID,
		&settings.Enabled,
		&settings.PaymentMethod,
		&paymentDataJSON,
		&settings.TariffCode,
		&settings.CreatedAt,
		&settings.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, err
		}
		return nil, fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	// Десериализуем JSON данные
	if paymentDataJSON != nil {
		err = json.Unmarshal(paymentDataJSON, &settings.SavedPaymentData)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal payment data: %w", err)
		}
	}

	return &settings, nil
}

// GetEnabledSettingsForRenewal - получает все включенные настройки автопродления для пользователей с истекающими подписками
func (r *AutoRenewalRepository) GetEnabledSettingsForRenewal(ctx context.Context, beforeTime time.Time) ([]AutoRenewalSettings, error) {
	// Запрос объединяет таблицы auto_renewal_settings и customer для получения пользователей с истекающими подписками
	query := `
		SELECT ars.id, ars.customer_id, ars.enabled, ars.payment_method, ars.saved_payment_data, 
		       ars.tariff_code, ars.created_at, ars.updated_at
		FROM auto_renewal_settings ars
		JOIN customer c ON ars.customer_id = c.id
		WHERE ars.enabled = true 
		  AND c.expire_at IS NOT NULL 
		  AND c.expire_at <= $1
		  AND c.expire_at > NOW()
	`

	rows, err := r.pool.Query(ctx, query, beforeTime)
	if err != nil {
		return nil, fmt.Errorf("failed to query enabled settings for renewal: %w", err)
	}
	defer rows.Close()

	var settings []AutoRenewalSettings
	for rows.Next() {
		var setting AutoRenewalSettings
		var paymentDataJSON []byte

		err := rows.Scan(
			&setting.ID,
			&setting.CustomerID,
			&setting.Enabled,
			&setting.PaymentMethod,
			&paymentDataJSON,
			&setting.TariffCode,
			&setting.CreatedAt,
			&setting.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan auto renewal setting: %w", err)
		}

		// Десериализуем JSON данные
		if paymentDataJSON != nil {
			err = json.Unmarshal(paymentDataJSON, &setting.SavedPaymentData)
			if err != nil {
				return nil, fmt.Errorf("failed to unmarshal payment data: %w", err)
			}
		}

		settings = append(settings, setting)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate over auto renewal settings: %w", err)
	}

	return settings, nil
}

// DisableAutoRenewal - отключает автопродление для пользователя
func (r *AutoRenewalRepository) DisableAutoRenewal(ctx context.Context, customerID int64) error {
	buildUpdate := sq.Update("auto_renewal_settings").
		Set("enabled", false).
		Where(sq.Eq{"customer_id": customerID}).
		PlaceholderFormat(sq.Dollar)

	sql, args, err := buildUpdate.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build update query: %w", err)
	}

	_, err = r.pool.Exec(ctx, sql, args...)
	if err != nil {
		return fmt.Errorf("failed to disable auto renewal: %w", err)
	}

	return nil
}

// DeleteSettings - удаляет настройки автопродления для пользователя
func (r *AutoRenewalRepository) DeleteSettings(ctx context.Context, customerID int64) error {
	buildDelete := sq.Delete("auto_renewal_settings").
		Where(sq.Eq{"customer_id": customerID}).
		PlaceholderFormat(sq.Dollar)

	sql, args, err := buildDelete.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build delete query: %w", err)
	}

	_, err = r.pool.Exec(ctx, sql, args...)
	if err != nil {
		return fmt.Errorf("failed to delete auto renewal settings: %w", err)
	}

	return nil
}

// CreateHistoryRecord - создает запись в истории автопродлений
func (r *AutoRenewalRepository) CreateHistoryRecord(ctx context.Context, history *AutoRenewalHistory) error {
	buildInsert := sq.Insert("auto_renewal_history").
		Columns("customer_id", "purchase_id", "auto_renewal_settings_id", "status", "error_message", "amount", "currency", "payment_method", "tariff_code").
		Values(history.CustomerID, history.PurchaseID, history.AutoRenewalSettingsID, history.Status, history.ErrorMessage, history.Amount, history.Currency, history.PaymentMethod, history.TariffCode).
		Suffix("RETURNING id, processed_at").
		PlaceholderFormat(sq.Dollar)

	sql, args, err := buildInsert.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build insert query: %w", err)
	}

	err = r.pool.QueryRow(ctx, sql, args...).Scan(&history.ID, &history.ProcessedAt)
	if err != nil {
		return fmt.Errorf("failed to create auto renewal history record: %w", err)
	}

	return nil
}

// GetHistoryByCustomerID - получает историю автопродлений для пользователя
func (r *AutoRenewalRepository) GetHistoryByCustomerID(ctx context.Context, customerID int64, limit int) ([]AutoRenewalHistory, error) {
	buildSelect := sq.Select("id", "customer_id", "purchase_id", "auto_renewal_settings_id", "status", "error_message", "amount", "currency", "payment_method", "tariff_code", "processed_at").
		From("auto_renewal_history").
		Where(sq.Eq{"customer_id": customerID}).
		OrderBy("processed_at DESC").
		Limit(uint64(limit)).
		PlaceholderFormat(sq.Dollar)

	sql, args, err := buildSelect.ToSql()
	if err != nil {
		return nil, fmt.Errorf("failed to build select query: %w", err)
	}

	rows, err := r.pool.Query(ctx, sql, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query auto renewal history: %w", err)
	}
	defer rows.Close()

	var history []AutoRenewalHistory
	for rows.Next() {
		var record AutoRenewalHistory

		err := rows.Scan(
			&record.ID,
			&record.CustomerID,
			&record.PurchaseID,
			&record.AutoRenewalSettingsID,
			&record.Status,
			&record.ErrorMessage,
			&record.Amount,
			&record.Currency,
			&record.PaymentMethod,
			&record.TariffCode,
			&record.ProcessedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan auto renewal history record: %w", err)
		}

		history = append(history, record)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate over auto renewal history: %w", err)
	}

	return history, nil
}

// CreateNotification - создает запись об отправленном уведомлении
func (r *AutoRenewalRepository) CreateNotification(ctx context.Context, notification *AutoRenewalNotification) error {
	buildInsert := sq.Insert("auto_renewal_notifications").
		Columns("customer_id", "notification_type", "subscription_expires_at").
		Values(notification.CustomerID, notification.NotificationType, notification.SubscriptionExpiresAt).
		Suffix("RETURNING id, sent_at").
		PlaceholderFormat(sq.Dollar)

	sql, args, err := buildInsert.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build insert query: %w", err)
	}

	err = r.pool.QueryRow(ctx, sql, args...).Scan(&notification.ID, &notification.SentAt)
	if err != nil {
		return fmt.Errorf("failed to create auto renewal notification: %w", err)
	}

	return nil
}

// HasNotificationBeenSent - проверяет, было ли уже отправлено уведомление данного типа для данной даты окончания подписки
func (r *AutoRenewalRepository) HasNotificationBeenSent(ctx context.Context, customerID int64, notificationType AutoRenewalNotificationType, expiresAt time.Time) (bool, error) {
	buildSelect := sq.Select("COUNT(*)").
		From("auto_renewal_notifications").
		Where(sq.And{
			sq.Eq{"customer_id": customerID},
			sq.Eq{"notification_type": notificationType},
			sq.Eq{"subscription_expires_at": expiresAt},
		}).
		PlaceholderFormat(sq.Dollar)

	sql, args, err := buildSelect.ToSql()
	if err != nil {
		return false, fmt.Errorf("failed to build select query: %w", err)
	}

	var count int
	err = r.pool.QueryRow(ctx, sql, args...).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("failed to check notification existence: %w", err)
	}

	return count > 0, nil
}

// GetCustomersForNotification - получает пользователей, которым нужно отправить уведомления определенного типа
func (r *AutoRenewalRepository) GetCustomersForNotification(ctx context.Context, notificationType AutoRenewalNotificationType, timeWindow time.Duration) ([]Customer, error) {
	var timeCondition string
	now := time.Now()

	switch notificationType {
	case AutoRenewalNotification3d:
		// За 3 дня до окончания подписки
		timeCondition = "c.expire_at BETWEEN $1 AND $2"
	case AutoRenewalNotification1d:
		// За 1 день до окончания подписки
		timeCondition = "c.expire_at BETWEEN $1 AND $2"
	case AutoRenewalNotification6h:
		// За 6 часов до окончания подписки
		timeCondition = "c.expire_at BETWEEN $1 AND $2"
	case AutoRenewalNotification1h:
		// За 1 час до окончания подписки
		timeCondition = "c.expire_at BETWEEN $1 AND $2"
	case AutoRenewalNotificationExpired:
		// После истечения подписки (в течение 1 часа после истечения)
		timeCondition = "c.expire_at BETWEEN $1 AND $2"
	default:
		return nil, fmt.Errorf("unsupported notification type: %s", notificationType)
	}

	query := fmt.Sprintf(`
		SELECT DISTINCT c.id, c.telegram_id, c.expire_at, c.created_at, c.subscription_link, c.language
		FROM customer c
		JOIN auto_renewal_settings ars ON c.id = ars.customer_id
		WHERE ars.enabled = true
		  AND c.expire_at IS NOT NULL
		  AND %s
		  AND NOT EXISTS (
		      SELECT 1 FROM auto_renewal_notifications arn
		      WHERE arn.customer_id = c.id
		        AND arn.notification_type = $3
		        AND arn.subscription_expires_at = c.expire_at
		  )
	`, timeCondition)

	startTime := now.Add(timeWindow - time.Hour) // Начало окна
	endTime := now.Add(timeWindow + time.Hour)   // Конец окна (с запасом в час)

	rows, err := r.pool.Query(ctx, query, startTime, endTime, notificationType)
	if err != nil {
		return nil, fmt.Errorf("failed to query customers for notification: %w", err)
	}
	defer rows.Close()

	var customers []Customer
	for rows.Next() {
		var customer Customer

		err := rows.Scan(
			&customer.ID,
			&customer.TelegramID,
			&customer.ExpireAt,
			&customer.CreatedAt,
			&customer.SubscriptionLink,
			&customer.Language,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan customer: %w", err)
		}

		customers = append(customers, customer)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate over customers: %w", err)
	}

	return customers, nil
}

// UpdateHistoryRecord - обновляет запись в истории автопродлений
func (r *AutoRenewalRepository) UpdateHistoryRecord(ctx context.Context, historyID int64, status AutoRenewalHistoryStatus, purchaseID *int64, errorMessage *string) error {
	buildUpdate := sq.Update("auto_renewal_history").
		Set("status", status).
		Where(sq.Eq{"id": historyID}).
		PlaceholderFormat(sq.Dollar)

	if purchaseID != nil {
		buildUpdate = buildUpdate.Set("purchase_id", *purchaseID)
	}

	if errorMessage != nil {
		buildUpdate = buildUpdate.Set("error_message", *errorMessage)
	}

	sql, args, err := buildUpdate.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build update query: %w", err)
	}

	_, err = r.pool.Exec(ctx, sql, args...)
	if err != nil {
		return fmt.Errorf("failed to update auto renewal history record: %w", err)
	}

	return nil
}

// UpdateHistoryRecordWithSuccess - обновляет запись в истории как успешную
func (r *AutoRenewalRepository) UpdateHistoryRecordWithSuccess(ctx context.Context, historyID int64, purchaseID int64, amount float64, currency string) error {
	buildUpdate := sq.Update("auto_renewal_history").
		Set("status", AutoRenewalHistoryStatusSuccess).
		Set("purchase_id", purchaseID).
		Set("amount", amount).
		Set("currency", currency).
		Where(sq.Eq{"id": historyID}).
		PlaceholderFormat(sq.Dollar)

	sql, args, err := buildUpdate.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build update query: %w", err)
	}

	_, err = r.pool.Exec(ctx, sql, args...)
	if err != nil {
		return fmt.Errorf("failed to update auto renewal history record with success: %w", err)
	}

	return nil
}

// UpdateHistoryRecordWithError - обновляет запись в истории как неудачную
func (r *AutoRenewalRepository) UpdateHistoryRecordWithError(ctx context.Context, historyID int64, errorMessage string) error {
	buildUpdate := sq.Update("auto_renewal_history").
		Set("status", AutoRenewalHistoryStatusFailed).
		Set("error_message", errorMessage).
		Where(sq.Eq{"id": historyID}).
		PlaceholderFormat(sq.Dollar)

	sql, args, err := buildUpdate.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build update query: %w", err)
	}

	_, err = r.pool.Exec(ctx, sql, args...)
	if err != nil {
		return fmt.Errorf("failed to update auto renewal history record with error: %w", err)
	}

	return nil
}

// GetHistoryRecordByID - получает запись истории автопродления по ID
func (r *AutoRenewalRepository) GetHistoryRecordByID(ctx context.Context, historyID int64) (*AutoRenewalHistory, error) {
	buildSelect := sq.Select("id", "customer_id", "purchase_id", "auto_renewal_settings_id", "status", "error_message", "amount", "currency", "payment_method", "tariff_code", "processed_at").
		From("auto_renewal_history").
		Where(sq.Eq{"id": historyID}).
		PlaceholderFormat(sq.Dollar)

	sql, args, err := buildSelect.ToSql()
	if err != nil {
		return nil, fmt.Errorf("failed to build select query: %w", err)
	}

	var record AutoRenewalHistory
	err = r.pool.QueryRow(ctx, sql, args...).Scan(
		&record.ID,
		&record.CustomerID,
		&record.PurchaseID,
		&record.AutoRenewalSettingsID,
		&record.Status,
		&record.ErrorMessage,
		&record.Amount,
		&record.Currency,
		&record.PaymentMethod,
		&record.TariffCode,
		&record.ProcessedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, err
		}
		return nil, fmt.Errorf("failed to get auto renewal history record: %w", err)
	}

	return &record, nil
}

// GetPendingHistoryRecords - получает все записи истории со статусом pending
func (r *AutoRenewalRepository) GetPendingHistoryRecords(ctx context.Context) ([]AutoRenewalHistory, error) {
	buildSelect := sq.Select("id", "customer_id", "purchase_id", "auto_renewal_settings_id", "status", "error_message", "amount", "currency", "payment_method", "tariff_code", "processed_at").
		From("auto_renewal_history").
		Where(sq.Eq{"status": AutoRenewalHistoryStatusPending}).
		OrderBy("processed_at ASC").
		PlaceholderFormat(sq.Dollar)

	sql, args, err := buildSelect.ToSql()
	if err != nil {
		return nil, fmt.Errorf("failed to build select query: %w", err)
	}

	rows, err := r.pool.Query(ctx, sql, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query pending auto renewal history: %w", err)
	}
	defer rows.Close()

	var records []AutoRenewalHistory
	for rows.Next() {
		var record AutoRenewalHistory

		err := rows.Scan(
			&record.ID,
			&record.CustomerID,
			&record.PurchaseID,
			&record.AutoRenewalSettingsID,
			&record.Status,
			&record.ErrorMessage,
			&record.Amount,
			&record.Currency,
			&record.PaymentMethod,
			&record.TariffCode,
			&record.ProcessedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan auto renewal history record: %w", err)
		}

		records = append(records, record)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate over auto renewal history: %w", err)
	}

	return records, nil
}
