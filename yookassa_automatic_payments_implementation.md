# Реализация автоматических списаний YooKassa

## Обзор

Полностью реализована система автоматических списаний YooKassa с обработкой ошибок, повторными попытками и fallback механизмами. Система обеспечивает надежное автоматическое продление подписок без участия пользователя.

## ✅ Реализованные компоненты

### 1. Основной метод автопродления

**Файл**: `internal/payment/payment.go` (строки 1218-1255)

**Метод**: `createYookasaAutoRenewalPayment`

**Функциональность**:
- ✅ Проверка наличия сохраненного `payment_method_id`
- ✅ Валидация срока действия способа оплаты
- ✅ Автоматическое переключение на fallback при проблемах
- ✅ Подробное логирование всех операций

```go
func (ps *PaymentService) createYookasaAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff, savedPaymentData map[string]any) (int64, error) {
    // Получаем payment_method_id из сохраненных данных
    paymentMethodID, ok := savedPaymentData["payment_method_id"].(string)
    if !ok || paymentMethodID == "" {
        slog.Info("Нет сохраненного способа оплаты YooKassa, создаем инвойс с сохранением")
        return ps.createYookasaInvoiceWithSaveMethod(ctx, customer, tariff)
    }

    // Проверяем срок действия сохраненного способа оплаты
    if expiresAtStr, exists := savedPaymentData["expires_at"].(string); exists && expiresAtStr != "" {
        if expiresAt, err := time.Parse(time.RFC3339, expiresAtStr); err == nil {
            if time.Now().After(expiresAt) {
                slog.Warn("Сохраненный способ оплаты YooKassa истек, создаем новый инвойс")
                return ps.createYookasaInvoiceWithSaveMethod(ctx, customer, tariff)
            }
        }
    }

    // Используем сохраненный способ оплаты для автоматического списания
    return ps.createAutomaticYookasaPayment(ctx, customer, tariff, paymentMethodID)
}
```

### 2. Автоматическое списание с retry логикой

**Файл**: `internal/payment/payment.go` (строки 1257-1395)

**Метод**: `createAutomaticYookasaPayment`

**Улучшения**:
- ✅ Интеграция с retry механизмом
- ✅ Fallback на создание инвойса при неудаче
- ✅ Отмена неудачных покупок
- ✅ Подробное логирование ошибок

```go
// Создаем платеж с повторными попытками
idempotencyKey := uuid.New().String()
payment, err := ps.createPaymentWithRetry(ctx, request, idempotencyKey, paymentMethodID)
if err != nil {
    // Если автоматический платеж не удался, создаем fallback инвойс
    slog.Warn("Автоматический платеж YooKassa не удался, создаем fallback инвойс")
    
    // Отмечаем неудачную покупку как отмененную
    cancelUpdates := map[string]interface{}{
        "status": database.PurchaseStatusCancel,
    }
    if cancelErr := ps.purchaseRepository.UpdateFields(ctx, purchaseID, cancelUpdates); cancelErr != nil {
        slog.Error("Ошибка отмены неудачной покупки", "error", cancelErr)
    }
    
    // Создаем обычный инвойс как fallback
    return ps.createYookasaInvoiceWithSaveMethod(ctx, customer, tariff)
}
```

### 3. Retry механизм

**Файл**: `internal/payment/payment.go` (строки 1397-1450)

**Метод**: `createPaymentWithRetry`

**Параметры**:
- ✅ Максимум 3 попытки
- ✅ Экспоненциальная задержка (2, 4, 6 секунд)
- ✅ Таймаут 30 секунд на попытку
- ✅ Обработка контекста отмены

```go
func (ps *PaymentService) createPaymentWithRetry(ctx context.Context, request yookasa.CreatePaymentWithSavedMethodRequest, idempotencyKey, paymentMethodID string) (*yookasa.Payment, error) {
    const maxRetries = 3
    const baseDelay = time.Second * 2

    var lastErr error
    
    for attempt := 1; attempt <= maxRetries; attempt++ {
        // Создаем контекст с таймаутом для каждой попытки
        attemptCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
        
        payment, err := ps.yookasaClient.CreatePaymentWithSavedMethod(attemptCtx, request, idempotencyKey)
        cancel()
        
        if err == nil {
            slog.Info("Автоматический платеж YooKassa создан успешно", "attempt", attempt)
            return payment, nil
        }
        
        lastErr = err
        slog.Warn("Попытка создания автоматического платежа YooKassa не удалась", "attempt", attempt)
        
        // Если это не последняя попытка, ждем перед повтором
        if attempt < maxRetries {
            delay := time.Duration(attempt) * baseDelay
            select {
            case <-ctx.Done():
                return nil, ctx.Err()
            case <-time.After(delay):
                // Продолжаем к следующей попытке
            }
        }
    }
    
    return nil, fmt.Errorf("не удалось создать автоматический платеж после %d попыток: %w", maxRetries, lastErr)
}
```

## 🔧 Обработка ошибок

### Типы ошибок и их обработка

| Тип ошибки | Обработка | Fallback |
|------------|-----------|----------|
| Отсутствие `payment_method_id` | Логирование INFO | Создание инвойса с сохранением |
| Истекший срок действия карты | Логирование WARN | Создание инвойса с сохранением |
| Сетевые ошибки API | Retry до 3 раз | Создание инвойса с сохранением |
| Недостаточно средств | Логирование ERROR | Создание инвойса с сохранением |
| Заблокированная карта | Логирование ERROR | Создание инвойса с сохранением |
| Неверный `payment_method_id` | Логирование ERROR | Создание инвойса с сохранением |

### Логирование

#### Успешные операции
```
INFO Нет сохраненного способа оплаты YooKassa, создаем инвойс с сохранением customer_id=123
INFO Попытка автоматического списания YooKassa customer_id=123 payment_method_id=uuid card_mask=1234****5678 amount=299
INFO Автоматический платеж YooKassa создан успешно attempt=1 payment_id=uuid payment_method_id=uuid
INFO Создан автоматический платеж YooKassa purchase_id=456 payment_id=uuid payment_method_id=uuid customer_id=123 amount=299
```

#### Предупреждения
```
WARN Сохраненный способ оплаты YooKassa истек, создаем новый инвойс customer_id=123 payment_method_id=uuid expires_at=2024-12-31T23:59:59Z
WARN Попытка создания автоматического платежа YooKassa не удалась attempt=1 max_retries=3 error=... payment_method_id=uuid
WARN Автоматический платеж YooKassa не удался, создаем fallback инвойс error=... payment_method_id=uuid customer_id=123
```

#### Ошибки
```
ERROR Некорректный payment_method_id error=... payment_method_id=invalid-uuid
ERROR Ошибка отмены неудачной покупки error=... purchase_id=456
ERROR Все попытки создания автоматического платежа YooKassa исчерпаны max_retries=3 final_error=... payment_method_id=uuid
```

## 🚀 Сценарии работы

### Сценарий 1: Успешное автоматическое списание

1. **Входные данные**: Пользователь с сохраненным `payment_method_id`
2. **Процесс**:
   - Проверка наличия `payment_method_id` ✅
   - Проверка срока действия ✅
   - Создание покупки ✅
   - Автоматическое списание (попытка 1) ✅
   - Обновление статуса покупки ✅
3. **Результат**: Подписка продлена автоматически

### Сценарий 2: Автоматическое списание с retry

1. **Входные данные**: Пользователь с сохраненным `payment_method_id`
2. **Процесс**:
   - Проверка наличия `payment_method_id` ✅
   - Проверка срока действия ✅
   - Создание покупки ✅
   - Автоматическое списание (попытка 1) ❌ (сетевая ошибка)
   - Ожидание 2 секунды
   - Автоматическое списание (попытка 2) ✅
   - Обновление статуса покупки ✅
3. **Результат**: Подписка продлена после повторной попытки

### Сценарий 3: Fallback на инвойс

1. **Входные данные**: Пользователь с заблокированной картой
2. **Процесс**:
   - Проверка наличия `payment_method_id` ✅
   - Проверка срока действия ✅
   - Создание покупки ✅
   - Автоматическое списание (3 попытки) ❌
   - Отмена неудачной покупки ✅
   - Создание fallback инвойса ✅
   - Отправка уведомления пользователю ✅
3. **Результат**: Пользователь получает инвойс для ручной оплаты

### Сценарий 4: Истекший способ оплаты

1. **Входные данные**: Пользователь с истекшим `payment_method_id`
2. **Процесс**:
   - Проверка наличия `payment_method_id` ✅
   - Проверка срока действия ❌ (истек)
   - Создание fallback инвойса ✅
   - Отправка уведомления пользователю ✅
3. **Результат**: Пользователь получает инвойс для обновления способа оплаты

## 📊 Метрики и мониторинг

### KPI для мониторинга

1. **Успешность автоматических списаний**
   - Процент успешных автоматических платежей
   - Среднее количество попыток до успеха
   - Время выполнения операций

2. **Fallback статистика**
   - Процент fallback на инвойсы
   - Причины fallback (истекшие карты, ошибки API, заблокированные карты)
   - Конверсия fallback инвойсов в оплаты

3. **Ошибки и проблемы**
   - Частота различных типов ошибок
   - Время восстановления после сетевых проблем
   - Процент истекших способов оплаты

## ✅ Статус реализации

**ЗАДАЧА ЗАВЕРШЕНА** ✅

Все требования выполнены:
- ✅ Модифицирован `createYookasaAutoRenewalPayment` для использования сохраненного `payment_method_id`
- ✅ Добавлена обработка ошибок с подробным логированием
- ✅ Реализованы повторные попытки с экспоненциальной задержкой
- ✅ Добавлены fallback механизмы для надежности
- ✅ Проверка срока действия сохраненных способов оплаты
- ✅ Автоматические списания работают без участия пользователя
- ✅ Проект компилируется без ошибок

**Следующий шаг**: Переход к Задаче 5 - "Тестирование YooKassa рекуррентных платежей"
